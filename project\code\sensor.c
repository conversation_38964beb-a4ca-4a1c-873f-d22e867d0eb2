#include "sensor.h"
#include "zf_device_ganwei_grayscale.h"

//====================================================全局变量====================================================
static ganwei_grayscale_info_struct sensor_info;
static sensor_data_t current_data = {0};
static uint8 sensor_initialized = 0;

// 权重系数（用于计算线位置）
static const float position_weights[8] = {-4.0f, -2.86f, -1.71f, -0.57f, 0.57f, 1.71f, 2.86f, 4.0f};

//====================================================函数实现====================================================
/**
 * @brief 传感器初始化
 */
uint8 sensor_init(void)
{
    // 初始化灰度传感器
    if(!ganwei_grayscale_init(&sensor_info, 
                              GANWEI_GRAYSCALE_CLASS_EDITION,
                              GANWEI_GRAYSCALE_ADC_12BITS,
                              SENSOR_ADDR0_PIN,
                              SENSOR_ADDR1_PIN,
                              SENSOR_ADDR2_PIN,
                              SENSOR_ADC_PIN))
    {
        DEBUG_PRINT("传感器初始化失败\r\n");
        return 0;
    }
    
    // 设置默认阈值
    ganwei_grayscale_set_threshold(&sensor_info, SENSOR_THRESHOLD);
    
    sensor_initialized = 1;
    DEBUG_PRINT("传感器初始化成功\r\n");
    return 1;
}

/**
 * @brief 读取传感器数据
 */
sensor_data_t sensor_read(void)
{
    if(!sensor_initialized)
    {
        current_data.line_detected = 0;
        return current_data;
    }
    
    // 读取数字数据
    current_data.digital_data = ganwei_grayscale_get_digital(&sensor_info);
    
    // 读取模拟数据
    current_data.analog_data = ganwei_grayscale_get_analog(&sensor_info);
    
    // 计算线位置
    current_data.line_position = sensor_get_line_position();
    
    // 检查是否检测到线
    current_data.line_detected = (current_data.digital_data != 0) ? 1 : 0;
    
    // 更新检测时间
    if(current_data.line_detected)
    {
        current_data.last_detect_time = system_time_ms;
    }
    
    return current_data;
}

/**
 * @brief 获取线位置
 */
float sensor_get_line_position(void)
{
    uint8 digital_data = current_data.digital_data;
    
    // 如果没有检测到线，返回上次位置
    if(digital_data == 0)
    {
        return current_data.line_position;
    }
    
    // 计算加权平均位置
    float weighted_sum = 0.0f;
    float total_weight = 0.0f;
    
    for(int i = 0; i < SENSOR_COUNT; i++)
    {
        if(digital_data & (1 << i))
        {
            weighted_sum += position_weights[i];
            total_weight += 1.0f;
        }
    }
    
    if(total_weight > 0)
    {
        return weighted_sum / total_weight;
    }
    
    return 0.0f;
}

/**
 * @brief 检查是否检测到线
 */
uint8 sensor_is_line_detected(void)
{
    return current_data.line_detected;
}

/**
 * @brief 传感器校准（简化版）
 */
uint8 sensor_calibrate(void)
{
    if(!sensor_initialized)
    {
        return 0;
    }
    
    DEBUG_PRINT("开始传感器校准...\r\n");
    
    // 简单的校准：读取当前环境，设置合适的阈值
    uint16 analog_value = ganwei_grayscale_get_analog(&sensor_info);
    
    // 根据当前读数调整阈值
    uint16 new_threshold = analog_value + 500;  // 简单的阈值设置
    if(new_threshold > 4000) new_threshold = 4000;
    if(new_threshold < 1000) new_threshold = 1000;
    
    ganwei_grayscale_set_threshold(&sensor_info, new_threshold);
    
    DEBUG_PRINT("校准完成，阈值: %d\r\n", new_threshold);
    return 1;
}
