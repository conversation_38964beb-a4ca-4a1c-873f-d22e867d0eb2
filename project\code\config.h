#ifndef __CONFIG_H__
#define __CONFIG_H__

#include "zf_common_headfile.h"

//====================================================系统配置====================================================
#define SYSTEM_FREQUENCY        80000000    // 系统频率80MHz
#define CONTROL_PERIOD_MS       10          // 控制周期10ms
#define STARTUP_DELAY_MS        3000        // 启动延时3秒

//====================================================硬件引脚配置====================================================
// 传感器引脚
#define SENSOR_ADDR0_PIN        A0          // 地址线0
#define SENSOR_ADDR1_PIN        A1          // 地址线1  
#define SENSOR_ADDR2_PIN        A2          // 地址线2
#define SENSOR_ADC_PIN          ADC_0       // ADC通道

// 电机引脚
#define MOTOR_LEFT_PWM          PWM_CH2     // 左电机PWM
#define MOTOR_RIGHT_PWM         PWM_CH3     // 右电机PWM
#define MOTOR_LEFT_DIR1         A16         // 左电机方向1
#define MOTOR_LEFT_DIR2         A17         // 左电机方向2
#define MOTOR_RIGHT_DIR1        A18         // 右电机方向1
#define MOTOR_RIGHT_DIR2        A19         // 右电机方向2
#define MOTOR_ENABLE_PIN        A20         // 电机使能

// 状态指示
#define STATUS_LED_PIN          A14         // 状态LED

//====================================================传感器配置====================================================
#define SENSOR_COUNT            8           // 传感器数量
#define SENSOR_THRESHOLD        2000        // 黑白阈值
#define LINE_LOST_THRESHOLD     50          // 丢线判断阈值(ms)
#define LINE_LOST_TIMEOUT       1000        // 丢线超时(ms)

//====================================================电机配置====================================================
#define MOTOR_PWM_FREQUENCY     20000       // PWM频率20kHz
#define MOTOR_BASE_SPEED        35          // 基础速度
#define MOTOR_MAX_SPEED         80          // 最大速度
#define MOTOR_MIN_SPEED         10          // 最小速度
#define MOTOR_DEAD_ZONE         15          // 死区补偿

//====================================================PID配置====================================================
// 方向环PID参数（控制转向）
#define DIRECTION_KP            12.0f       // 比例系数
#define DIRECTION_KI            0.0f        // 积分系数  
#define DIRECTION_KD            180.0f      // 微分系数
#define DIRECTION_OUTPUT_LIMIT  50.0f       // 输出限幅

// 速度环PID参数（控制速度，可选）
#define SPEED_KP                18.0f       // 比例系数
#define SPEED_KI                0.2f        // 积分系数
#define SPEED_KD                0.0f        // 微分系数
#define SPEED_OUTPUT_LIMIT      100.0f      // 输出限幅

//====================================================系统状态====================================================
typedef enum {
    SYSTEM_INIT = 0,        // 系统初始化
    SYSTEM_READY,           // 系统就绪
    SYSTEM_RUNNING,         // 正在寻迹
    SYSTEM_LINE_LOST,       // 丢线状态
    SYSTEM_ERROR            // 系统错误
} system_state_t;

//====================================================全局变量声明====================================================
extern volatile uint32 system_time_ms;     // 系统时间(毫秒)
extern system_state_t system_state;        // 系统状态

//====================================================调试配置====================================================
#ifdef DEBUG
    #define DEBUG_PRINT(fmt, ...) printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(fmt, ...)
#endif

#endif // __CONFIG_H__
