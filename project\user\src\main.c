/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          mian
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
********************************************************************************************************************/

#include "zf_common_headfile.h"
#include "project/code/config.h"
#include "project/code/app/line_follow_app.h"
#include "project/code/scheduler/scheduler.h"
#include "project/code/driver/encoder_driver.h"

// 打开新的工程或者工程移动了位置务必执行以下操作
// 第一步 关闭上面所有打开的文件
// 第二步 project->clean  等待下方进度条走完

//====================================================全局变量====================================================
volatile uint32 system_time_ms = 0;        // 系统时间计数器
system_state_t system_state = SYSTEM_INIT; // 系统状态

//====================================================系统初始化====================================================
/**
 * @brief 系统初始化
 */
uint8 system_init(void)
{
    // 基础系统初始化
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();                   // 调试串口信息初始化

    // 初始化状态LED
    gpio_init(STATUS_LED_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);

    // 初始化系统时间
    system_time_ms = 0;

    // 初始化调度器
    if(!scheduler_init())
    {
        system_state = SYSTEM_ERROR;
        return 0;
    }

    // 初始化寻迹应用
    if(!line_follow_app_init())
    {
        system_state = SYSTEM_ERROR;
        return 0;
    }

    // 初始化编码器（可选）
    encoder_init();

    // 添加任务到调度器
    scheduler_add_task(line_follow_app_task, CONTROL_PERIOD_MS);    // 寻迹控制任务
    scheduler_add_task(encoder_task, 50);                           // 编码器任务
    scheduler_add_task(status_led_task, 200);                       // LED状态指示任务

    system_state = SYSTEM_READY;
    return 1;
}

/**
 * @brief LED状态指示任务
 */
void status_led_task(void)
{
    static uint8 led_state = 0;
    static uint32 last_toggle_time = 0;
    uint32 current_time = system_time_ms;
    uint32 toggle_period = 500; // 默认500ms闪烁

    // 根据系统状态调整LED闪烁频率
    switch(system_state)
    {
        case SYSTEM_INIT:
            toggle_period = 200;    // 快速闪烁
            break;
        case SYSTEM_READY:
            toggle_period = 500;    // 中速闪烁
            break;
        case SYSTEM_RUNNING:
            toggle_period = 1000;   // 慢速闪烁
            break;
        case SYSTEM_LINE_LOST:
            toggle_period = 100;    // 极快闪烁
            break;
        case SYSTEM_ERROR:
            gpio_set_level(STATUS_LED_PIN, GPIO_HIGH); // 常亮
            return;
    }

    if(current_time - last_toggle_time >= toggle_period)
    {
        led_state = !led_state;
        gpio_set_level(STATUS_LED_PIN, led_state ? GPIO_HIGH : GPIO_LOW);
        last_toggle_time = current_time;
    }
}

/**
 * @brief 系统时间更新（1ms中断）
 */
void systick_handler(void)
{
    system_time_ms++;
}

/**
 * @brief 声明外部函数
 */
void status_led_task(void);

// **************************** 主程序 ****************************
int main (void)
{
    // 系统初始化
    if(!system_init())
    {
        // 初始化失败，LED常亮报警
        while(1)
        {
            gpio_set_level(STATUS_LED_PIN, GPIO_HIGH);
            system_delay_ms(100);
            gpio_set_level(STATUS_LED_PIN, GPIO_LOW);
            system_delay_ms(100);
        }
    }

    // 启动延时，给用户准备时间
    printf("System initialized successfully!\r\n");
    printf("Starting line following in %d seconds...\r\n", STARTUP_DELAY_MS/1000);

    uint32 start_time = system_time_ms;
    while(system_time_ms - start_time < STARTUP_DELAY_MS)
    {
        // 在等待期间运行调度器（主要是LED指示）
        scheduler_run();
        system_delay_ms(1);
    }

    // 启动寻迹
    if(line_follow_start())
    {
        system_state = SYSTEM_RUNNING;
        scheduler_start();
        printf("Line following started!\r\n");
    }
    else
    {
        system_state = SYSTEM_ERROR;
        printf("Failed to start line following!\r\n");
    }

    // 主循环
    while(true)
    {
        // 运行调度器
        scheduler_run();

        // 更新系统状态
        if(line_follow_is_running())
        {
            line_follow_status_t status = line_follow_get_status();
            if(status.state == LINE_FOLLOW_LINE_LOST)
            {
                system_state = SYSTEM_LINE_LOST;
            }
            else if(status.state == LINE_FOLLOW_RUNNING)
            {
                system_state = SYSTEM_RUNNING;
            }
        }
        else
        {
            system_state = SYSTEM_READY;
        }

        // 短暂延时，避免CPU占用过高
        system_delay_ms(1);
    }
}

