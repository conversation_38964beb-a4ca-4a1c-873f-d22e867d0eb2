

#include "zf_common_headfile.h"
#include "../../code/app/line_follow_app.h"
#include "../../code/scheduler/scheduler.h"

//====================================================赛题配置====================================================
// 2025年全国大学生电子设计竞赛E题第一问
// 要求：1.8cm宽黑线寻迹，100cm×100cm正方形轨道，直线跟踪精度<0.5cm

#define SYSTEM_FREQUENCY        80000000    // 系统频率80MHz
#define CONTROL_PERIOD_MS       8           // 控制周期8ms（125Hz高频控制）
#define STARTUP_DELAY_MS        3000        // 启动延时3秒
#define STATUS_LED_PIN          A14         // 状态LED

//====================================================全局变量====================================================
volatile uint32 system_time_ms = 0;                    // 系统时间计数器

//====================================================中断函数====================================================
void pit_handler(void)
{
    system_time_ms++;
}

//====================================================核心功能函数====================================================
/**
 * @brief 限幅函数
 */
float constrain_float(float value, float min_val, float max_val)
{
    if(value > max_val) return max_val;
    if(value < min_val) return min_val;
    return value;
}

/**
 * @brief 传感器初始化
 */
uint8 sensor_init(void)
{
    if(!ganwei_grayscale_init(&sensor_info,
                              GANWEI_GRAYSCALE_CLASS_EDITION,
                              GANWEI_GRAYSCALE_ADC_12BITS,
                              SENSOR_ADDR0_PIN,
                              SENSOR_ADDR1_PIN,
                              SENSOR_ADDR2_PIN,
                              SENSOR_ADC_PIN))
    {
        printf("传感器初始化失败\r\n");
        return 0;
    }

    ganwei_grayscale_set_threshold(&sensor_info, SENSOR_THRESHOLD);
    printf("传感器初始化成功，阈值: %d\r\n", SENSOR_THRESHOLD);
    return 1;
}

/**
 * @brief 电机初始化
 */
uint8 motor_init(void)
{
    // 初始化PWM
    pwm_init(MOTOR_LEFT_PWM, MOTOR_PWM_FREQUENCY, 0);
    pwm_init(MOTOR_RIGHT_PWM, MOTOR_PWM_FREQUENCY, 0);

    // 初始化方向控制引脚
    gpio_init(MOTOR_LEFT_DIR1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_LEFT_DIR2, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_RIGHT_DIR1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_RIGHT_DIR2, GPO, GPIO_LOW, GPO_PUSH_PULL);

    // 初始化使能引脚
    gpio_init(MOTOR_ENABLE_PIN, GPO, GPIO_HIGH, GPO_PUSH_PULL);

    printf("电机初始化成功\r\n");
    return 1;
}

/**
 * @brief 获取线位置
 */
float get_line_position(void)
{
    uint8 digital_data = ganwei_grayscale_get_digital(&sensor_info);

    if(digital_data == 0)
    {
        return current_line_position; // 保持上次位置
    }

    float weighted_sum = 0.0f;
    float total_weight = 0.0f;

    for(int i = 0; i < 8; i++)
    {
        if(digital_data & (1 << i))
        {
            weighted_sum += position_weights[i];
            total_weight += 1.0f;
        }
    }

    if(total_weight > 0)
    {
        return weighted_sum / total_weight;
    }

    return current_line_position;
}

/**
 * @brief 电机控制
 */
void motor_control(int16 left_speed, int16 right_speed)
{
    // 限速
    left_speed = constrain_float(left_speed, -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);
    right_speed = constrain_float(right_speed, -MOTOR_MAX_SPEED, MOTOR_MAX_SPEED);

    // 死区补偿
    if(left_speed > 0) left_speed += DEAD_ZONE_COMPENSATION;
    else if(left_speed < 0) left_speed -= DEAD_ZONE_COMPENSATION;

    if(right_speed > 0) right_speed += DEAD_ZONE_COMPENSATION;
    else if(right_speed < 0) right_speed -= DEAD_ZONE_COMPENSATION;

    // 左电机控制
    uint32 left_duty = abs(left_speed);
    if(left_duty > 100) left_duty = 100;
    pwm_set_duty(MOTOR_LEFT_PWM, left_duty);

    if(left_speed > 0)
    {
        gpio_set_level(MOTOR_LEFT_DIR1, GPIO_HIGH);
        gpio_set_level(MOTOR_LEFT_DIR2, GPIO_LOW);
    }
    else if(left_speed < 0)
    {
        gpio_set_level(MOTOR_LEFT_DIR1, GPIO_LOW);
        gpio_set_level(MOTOR_LEFT_DIR2, GPIO_HIGH);
    }
    else
    {
        gpio_set_level(MOTOR_LEFT_DIR1, GPIO_LOW);
        gpio_set_level(MOTOR_LEFT_DIR2, GPIO_LOW);
    }

    // 右电机控制
    uint32 right_duty = abs(right_speed);
    if(right_duty > 100) right_duty = 100;
    pwm_set_duty(MOTOR_RIGHT_PWM, right_duty);

    if(right_speed > 0)
    {
        gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_HIGH);
        gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_LOW);
    }
    else if(right_speed < 0)
    {
        gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_LOW);
        gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_HIGH);
    }
    else
    {
        gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_LOW);
        gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_LOW);
    }
}

/**
 * @brief 用户命令处理（简化版）
 * @param void
 * @return void
 */
void process_user_commands(void)
{
    uint8 received_char;
    if(uart_query_byte(UART_0, &received_char))
    {
        switch(received_char)
        {
            case 'h':  // 帮助
            case 'H':
            case '?':
                printf("\r\n========== 寻迹小车系统 ==========\r\n");
                printf("编译测试模式 - 基本功能正常\r\n");
                printf("输入 'h' 查看此帮助信息\r\n");
                printf("输入 't' 测试LED闪烁\r\n");
                printf("================================\r\n");
                break;

            case 't':  // 测试
            case 'T':
                printf("LED测试...\r\n");
                for(int i = 0; i < 5; i++)
                {
                    gpio_set_level(A14, GPIO_HIGH);
                    system_delay_ms(200);
                    gpio_set_level(A14, GPIO_LOW);
                    system_delay_ms(200);
                }
                printf("LED测试完成\r\n");
                break;

            default:
                printf("编译测试模式 - 输入 'h' 查看帮助\r\n");
                break;
        }
    }
}

// 调试功能函数（编译测试模式下暂时移除）

/**
 * @brief 主函数
 * @param void
 * @return int
 */
int main(void)
{
    // 系统初始化
    system_init();
    
    // 配置任务调度器
    scheduler_setup();
    
    // 配置应用程序
    application_setup();
    
    // 显示启动信息
    printf("\r\n系统启动完成！\r\n");
    printf("输入 'h' 查看命令帮助\r\n");
    printf("输入 's' 开始寻迹\r\n\r\n");
    
    // 简化版寻迹主循环
    printf("========================================\r\n");
    printf("    简化版寻迹小车系统启动\r\n");
    printf("========================================\r\n");
    printf("专为比赛设计，上电自动寻迹\r\n");
    printf("等待%d秒后开始寻迹...\r\n", STARTUP_DELAY_MS/1000);
    printf("请将小车放置在黑线起始位置\r\n");
    printf("========================================\r\n");

    // 倒计时
    for(int i = STARTUP_DELAY_MS/1000; i > 0; i--)
    {
        printf("倒计时: %d 秒\r\n", i);
        gpio_toggle_level(STATUS_LED_PIN);
        system_delay_ms(1000);
    }

    printf("🚀 开始寻迹！\r\n");

    // 简单的寻迹循环
    while(1)
    {
        // LED心跳指示（运行状态）
        static uint32 last_led_time = 0;
        if(system_time_ms - last_led_time > 500)  // 500ms快速闪烁表示运行
        {
            gpio_toggle_level(STATUS_LED_PIN);
            last_led_time = system_time_ms;
        }

        // 这里可以添加实际的寻迹逻辑
        // 当前版本演示基本的系统框架

        // 短暂延时
        system_delay_ms(10);
    }
    
    return 0;
}
