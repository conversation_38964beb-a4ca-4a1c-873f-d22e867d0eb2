/*********************************************************************************************************************
* MSPM0G3507 Opensource Library ����MSPM0G3507 ��Դ�⣩��һ�����ڹٷ� SDK �ӿڵĵ�������Դ��
* Copyright (c) 2022 SEEKFREE ��ɿƼ�
* 
* ���ļ��� MSPM0G3507 ��Դ���һ����
* 
* ����Դ��ʹ�� GPL3.0 ��Դ����֤Э�飬������μ� LICENSE �ļ�
*
* �ļ�����          zf_device_ganwei_grayscale
* ��˾����          �ɶ���ɿƼ����޹�˾
* �޸ļ�¼
* ����              ����                ��ע
* 2025-01-01        SeekFree            ��Ϊ8·�Ҷȴ���������
********************************************************************************************************************/

#ifndef _ZF_DEVICE_GANWEI_GRAYSCALE_H_
#define _ZF_DEVICE_GANWEI_GRAYSCALE_H_

#include "zf_common_typedef.h"
#include "zf_driver_gpio.h"                // ���ӣ����� gpio_pin_enum ����
#include "zf_driver_adc.h"                 // ���ӣ����� adc_channel_enum ����

//====================================================��Ϊ8·�Ҷȴ����� ��������====================================================
#define GANWEI_GRAYSCALE_CHANNEL_NUM        8   // ������ͨ������

//====================================================�������汾ö�ٶ���===========================================================
typedef enum
{
    GANWEI_GRAYSCALE_CLASS_EDITION = 0,    // ����洫����
    GANWEI_GRAYSCALE_YOUTH_EDITION = 1,    // �ഺ�洫����
} ganwei_grayscale_edition_enum;

//====================================================ADC�ֱ���ö�ٶ���===========================================================
typedef enum
{
    GANWEI_GRAYSCALE_ADC_8BITS  = 0,       // 8λADCģʽ
    GANWEI_GRAYSCALE_ADC_10BITS = 1,       // 10λADCģʽ
    GANWEI_GRAYSCALE_ADC_12BITS = 2,       // 12λADCģʽ
    GANWEI_GRAYSCALE_ADC_14BITS = 3,       // 14λADCģʽ
} ganwei_grayscale_adc_bits_enum;

//====================================================���ṹ�嶨��===============================================================
typedef struct
{
    // ---------------- ���ݻ��� ----------------
    uint16 analog_value[GANWEI_GRAYSCALE_CHANNEL_NUM];       // ԭʼADCֵ
    uint16 normal_value[GANWEI_GRAYSCALE_CHANNEL_NUM];       // ��һ��ֵ��0~1000��
    uint16 calibrated_white[GANWEI_GRAYSCALE_CHANNEL_NUM];   // �׵�У׼�ο�
    uint16 calibrated_black[GANWEI_GRAYSCALE_CHANNEL_NUM];   // �ڵ�У׼�ο�
    uint16 gray_white[GANWEI_GRAYSCALE_CHANNEL_NUM];         // ��ƽ���м���
    uint16 gray_black[GANWEI_GRAYSCALE_CHANNEL_NUM];         // ��ƽ���м���
    float  normal_factor[GANWEI_GRAYSCALE_CHANNEL_NUM];      // ��һ������

    // ---------------- ϵͳ���� ----------------
    float  adc_max_value;             // ADC���ֵ����255,1023��
    uint8  digital_value;             // ���������8λ��ÿλ����1ͨ����
    uint8  timeout_value;             // ��ʱʱ�䣨��λ��ms��
    uint8  tick_counter;              // �ڲ�ʹ�ü�����
    uint8  init_flag;                 // ��ʼ����ɱ�־��0��δ��ɣ�1������ɣ�

    // ---------------- Ӳ������ ----------------
    ganwei_grayscale_edition_enum   edition;            // �������汾
    ganwei_grayscale_adc_bits_enum  adc_resolution;     // ADC�ֱ���
    gpio_pin_enum                   addr_pin0;          // ��ַ�� A0
    gpio_pin_enum                   addr_pin1;          // ��ַ�� A1
    gpio_pin_enum                   addr_pin2;          // ��ַ�� A2
    adc_pin_enum                    adc_channel;        // ADCͨ��
    uint8                           direction_reverse;  // �������ת��0������1��ת��

} ganwei_grayscale_info_struct;

//====================================================��������====================================================================

uint8 ganwei_grayscale_init(
    ganwei_grayscale_info_struct *dev_info,
    ganwei_grayscale_edition_enum edition,
    ganwei_grayscale_adc_bits_enum adc_bits,
    gpio_pin_enum addr0,
    gpio_pin_enum addr1,
    gpio_pin_enum addr2,
    adc_pin_enum adc_ch
);

uint8 ganwei_grayscale_init_with_calibration(
    ganwei_grayscale_info_struct *dev_info,
    const uint16 *white_values,
    const uint16 *black_values
);

void ganwei_grayscale_task(
    ganwei_grayscale_info_struct *dev_info
);

uint8 ganwei_grayscale_get_digital(
    ganwei_grayscale_info_struct *dev_info
);

uint8 ganwei_grayscale_get_analog(
    ganwei_grayscale_info_struct *dev_info,
    uint16 *result
);

uint8 ganwei_grayscale_get_normalized(
    ganwei_grayscale_info_struct *dev_info,
    uint16 *result
);

void ganwei_grayscale_set_direction(
    ganwei_grayscale_info_struct *dev_info,
    uint8 reverse
);

#endif  // _ZF_DEVICE_GANWEI_GRAYSCALE_H_
