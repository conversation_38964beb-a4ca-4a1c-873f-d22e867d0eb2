#include "grayscale_sensor_driver.h"

//====================================================全局变量====================================================
static ganwei_grayscale_info_struct sensor_info;
static sensor_data_t current_sensor_data = {0};
static uint8 sensor_initialized = 0;

//====================================================函数实现====================================================
/**
 * @brief 传感器初始化
 */
uint8 sensor_init(void)
{
    if(!ganwei_grayscale_init(&sensor_info,
                              GANWEI_GRAYSCALE_CLASS_EDITION,
                              GANWEI_GRAYSCALE_ADC_12BITS,
                              SENSOR_ADDR0_PIN,
                              SENSOR_ADDR1_PIN,
                              SENSOR_ADDR2_PIN,
                              SENSOR_ADC_PIN))
    {
        return 0;
    }

    ganwei_grayscale_set_threshold(&sensor_info, SENSOR_THRESHOLD);
    sensor_initialized = 1;
    return 1;
}

/**
 * @brief 读取传感器数据
 */
sensor_data_t sensor_read(void)
{
    if(!sensor_initialized)
    {
        current_sensor_data.line_detected = 0;
        return current_sensor_data;
    }

    current_sensor_data.digital_data = ganwei_grayscale_get_digital(&sensor_info);
    current_sensor_data.analog_data = ganwei_grayscale_get_analog(&sensor_info);
    current_sensor_data.line_detected = (current_sensor_data.digital_data != 0) ? 1 : 0;

    return current_sensor_data;
}

/**
 * @brief 获取线位置
 */
float sensor_get_line_position(void)
{
    sensor_data_t data = sensor_read();

    if(!data.line_detected)
    {
        return current_sensor_data.line_position;
    }

    float weighted_sum = 0.0f;
    float total_weight = 0.0f;

    for(int i = 0; i < 8; i++)
    {
        if(data.digital_data & (1 << i))
        {
            weighted_sum += POSITION_WEIGHTS[i];
            total_weight += 1.0f;
        }
    }

    if(total_weight > 0)
    {
        current_sensor_data.line_position = weighted_sum / total_weight;
    }

    return current_sensor_data.line_position;
}

/**
 * @brief 检查是否检测到线
 */
uint8 sensor_is_line_detected(void)
{
    return current_sensor_data.line_detected;
}
