#include "grayscale_sensor_driver.h"
#include "../config.h"

//====================================================全局变量====================================================
static ganwei_grayscale_info_struct sensor_info;
static sensor_data_t current_sensor_data = {0};
static uint8 sensor_initialized = 0;

//====================================================函数实现====================================================
/**
 * @brief 传感器初始化
 */
uint8 sensor_init(void)
{
    if(!ganwei_grayscale_init(&sensor_info,
                              GANWEI_GRAYSCALE_CLASS_EDITION,
                              GANWEI_GRAYSCALE_ADC_12BITS,
                              SENSOR_ADDR0_PIN,
                              SENSOR_ADDR1_PIN,
                              SENSOR_ADDR2_PIN,
                              SENSOR_ADC_PIN))
    {
        return 0;
    }

    ganwei_grayscale_set_threshold(&sensor_info, SENSOR_THRESHOLD);
    sensor_initialized = 1;
    return 1;
}

/**
 * @brief 读取传感器数据
 */
sensor_data_t sensor_read(void)
{
    if(!sensor_initialized)
    {
        current_sensor_data.line_detected = 0;
        current_sensor_data.line_position = 0.0f;
        return current_sensor_data;
    }

    // 先运行传感器任务更新数据
    ganwei_grayscale_task(&sensor_info);

    current_sensor_data.digital_data = ganwei_grayscale_get_digital(&sensor_info);

    // 获取模拟数据需要传入数组指针
    uint16 analog_values[8];
    ganwei_grayscale_get_analog(&sensor_info, analog_values);
    current_sensor_data.analog_data = analog_values[0]; // 简化存储第一个值

    current_sensor_data.line_detected = (current_sensor_data.digital_data != 0) ? 1 : 0;

    // 计算线位置
    if(current_sensor_data.line_detected)
    {
        current_sensor_data.line_position = calculate_line_position(current_sensor_data.digital_data);
        current_sensor_data.last_detect_time = system_time_ms;
    }
    else
    {
        current_sensor_data.line_position = 0.0f;
    }

    return current_sensor_data;
}

/**
 * @brief 计算线位置（内部函数）
 */
static float calculate_line_position(uint8 digital_data)
{
    float weighted_sum = 0.0f;
    uint8 active_sensors = 0;

    // 遍历8个传感器
    for(uint8 i = 0; i < SENSOR_COUNT; i++)
    {
        if(digital_data & (1 << i))
        {
            weighted_sum += POSITION_WEIGHTS[i];
            active_sensors++;
        }
    }

    // 如果有传感器检测到线，返回加权平均位置
    if(active_sensors > 0)
    {
        return weighted_sum / active_sensors;
    }

    return 0.0f;
}

/**
 * @brief 获取线位置
 */
float sensor_get_line_position(void)
{
    if(!sensor_initialized) return 0.0f;

    // 读取最新传感器数据
    sensor_read();
    return current_sensor_data.line_position;
}

/**
 * @brief 检查是否检测到线
 */
uint8 sensor_is_line_detected(void)
{
    if(!sensor_initialized) return 0;

    // 读取最新传感器数据
    sensor_read();
    return current_sensor_data.line_detected;
}

/**
 * @brief 获取线位置
 */
float sensor_get_line_position(void)
{
    sensor_data_t data = sensor_read();

    if(!data.line_detected)
    {
        return current_sensor_data.line_position;
    }

    float weighted_sum = 0.0f;
    float total_weight = 0.0f;

    for(int i = 0; i < 8; i++)
    {
        if(data.digital_data & (1 << i))
        {
            weighted_sum += POSITION_WEIGHTS[i];
            total_weight += 1.0f;
        }
    }

    if(total_weight > 0)
    {
        current_sensor_data.line_position = weighted_sum / total_weight;
    }

    return current_sensor_data.line_position;
}

/**
 * @brief 检查是否检测到线
 */
uint8 sensor_is_line_detected(void)
{
    return current_sensor_data.line_detected;
}
