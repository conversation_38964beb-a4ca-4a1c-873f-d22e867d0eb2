#ifndef __PID_H__
#define __PID_H__

#include "config.h"

//====================================================数据结构====================================================
typedef struct {
    float kp;                   // 比例系数
    float ki;                   // 积分系数
    float kd;                   // 微分系数
    
    float target;               // 目标值
    float current;              // 当前值
    float error;                // 当前误差
    float last_error;           // 上次误差
    float integral;             // 积分累积
    float derivative;           // 微分值
    float output;               // 输出值
    
    float output_limit;         // 输出限幅
    float integral_limit;       // 积分限幅
    
    uint32 last_time;           // 上次计算时间
    uint8 first_run;            // 首次运行标志
} pid_controller_t;

//====================================================函数声明====================================================
/**
 * @brief PID控制器初始化
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @param output_limit 输出限幅
 * @return 成功返回1，失败返回0
 */
uint8 pid_init(pid_controller_t *pid, float kp, float ki, float kd, float output_limit);

/**
 * @brief PID计算
 * @param pid PID控制器指针
 * @param target 目标值
 * @param current 当前值
 * @return PID输出值
 */
float pid_calculate(pid_controller_t *pid, float target, float current);

/**
 * @brief 重置PID控制器
 * @param pid PID控制器指针
 * @return 成功返回1，失败返回0
 */
uint8 pid_reset(pid_controller_t *pid);

/**
 * @brief 设置PID参数
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 * @return 成功返回1，失败返回0
 */
uint8 pid_set_parameters(pid_controller_t *pid, float kp, float ki, float kd);

/**
 * @brief 限幅函数
 * @param value 输入值
 * @param min_val 最小值
 * @param max_val 最大值
 * @return 限幅后的值
 */
float pid_constrain(float value, float min_val, float max_val);

#endif // __PID_H__
