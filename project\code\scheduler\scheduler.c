#include "scheduler.h"

//====================================================全局变量====================================================
static scheduler_task_t task_list[MAX_TASKS];
static uint8 task_count = 0;
static uint8 scheduler_running = 0;

//====================================================函数实现====================================================
/**
 * @brief 调度器初始化
 */
uint8 scheduler_init(void)
{
    task_count = 0;
    scheduler_running = 0;

    // 清空任务列表
    for(int i = 0; i < MAX_TASKS; i++)
    {
        task_list[i].task_func = NULL;
        task_list[i].period_ms = 0;
        task_list[i].last_run_time = 0;
        task_list[i].enabled = 0;
    }

    return 1;
}

/**
 * @brief 添加任务
 */
uint8 scheduler_add_task(task_func_t task_func, uint32 period_ms)
{
    if(task_count >= MAX_TASKS || task_func == NULL) return 0;

    task_list[task_count].task_func = task_func;
    task_list[task_count].period_ms = period_ms;
    task_list[task_count].last_run_time = system_time_ms;
    task_list[task_count].enabled = 1;

    task_count++;
    return 1;
}

/**
 * @brief 启动调度器
 */
uint8 scheduler_start(void)
{
    scheduler_running = 1;
    return 1;
}

/**
 * @brief 停止调度器
 */
uint8 scheduler_stop(void)
{
    scheduler_running = 0;
    return 1;
}

/**
 * @brief 调度器运行
 */
void scheduler_run(void)
{
    if(!scheduler_running) return;

    uint32 current_time = system_time_ms;

    for(int i = 0; i < task_count; i++)
    {
        if(task_list[i].enabled && task_list[i].task_func != NULL)
        {
            if(current_time - task_list[i].last_run_time >= task_list[i].period_ms)
            {
                task_list[i].task_func();
                task_list[i].last_run_time = current_time;
            }
        }
    }
}
