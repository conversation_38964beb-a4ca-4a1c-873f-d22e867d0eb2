#ifndef __LINE_FOLLOW_APP_H__
#define __LINE_FOLLOW_APP_H__

#include "zf_common_headfile.h"
#include "../driver/grayscale_sensor_driver.h"
#include "../driver/tb6612_driver.h"
#include "../algorithm/pid_controller.h"

//====================================================配置参数====================================================
// 应用层配置
#define LINE_LOST_THRESHOLD     5           // 丢线判断阈值（控制周期数）
#define LINE_LOST_TIMEOUT       100         // 丢线超时（控制周期数）

// 外部变量声明
extern volatile uint32 system_time_ms;

//====================================================数据结构====================================================
typedef enum {
    LINE_FOLLOW_STOPPED = 0,    // 停止状态
    LINE_FOLLOW_RUNNING,        // 正常寻迹
    LINE_FOLLOW_LINE_LOST,      // 丢线状态
    LINE_FOLLOW_COMPLETED,      // 完成状态
    LINE_FOLLOW_ERROR           // 错误状态
} line_follow_state_t;

typedef struct {
    line_follow_state_t state;  // 当前状态
    float line_position;        // 当前线位置
    float direction_output;     // 方向控制输出
    int16 base_speed;           // 基础速度
    uint32 line_lost_count;     // 丢线计数
    uint32 control_loop_count;  // 控制循环计数
    uint32 start_time;          // 开始时间
    uint32 run_time;            // 运行时间
} line_follow_status_t;

//====================================================函数声明====================================================
/**
 * @brief 寻迹应用初始化
 * @return 成功返回1，失败返回0
 */
uint8 line_follow_app_init(void);

/**
 * @brief 开始寻迹
 * @return 成功返回1，失败返回0
 */
uint8 line_follow_start(void);

/**
 * @brief 停止寻迹
 * @return 成功返回1，失败返回0
 */
uint8 line_follow_stop(void);

/**
 * @brief 寻迹控制主循环
 * @return 成功返回1，失败返回0
 */
uint8 line_follow_control_loop(void);

/**
 * @brief 获取寻迹状态
 * @return 寻迹状态结构体
 */
line_follow_status_t line_follow_get_status(void);

/**
 * @brief 检查是否正在运行
 * @return 正在运行返回1，否则返回0
 */
uint8 line_follow_is_running(void);

/**
 * @brief 设置基础速度
 * @param speed 基础速度
 * @return 成功返回1，失败返回0
 */
uint8 line_follow_set_base_speed(int16 speed);

/**
 * @brief 应用层任务（供调度器调用）
 */
void line_follow_app_task(void);

#endif // __LINE_FOLLOW_APP_H__
