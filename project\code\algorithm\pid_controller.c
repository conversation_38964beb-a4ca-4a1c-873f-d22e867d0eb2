#include "pid_controller.h"
#include <math.h>

//====================================================外部变量====================================================
extern volatile uint32 system_time_ms;

//====================================================函数实现====================================================
/**
 * @brief PID控制器初始化
 */
uint8 pid_init(pid_controller_t *pid, float kp, float ki, float kd, float output_limit)
{
    if(pid == NULL) return 0;

    // 设置PID参数
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;

    // 初始化状态变量
    pid->target = 0.0f;
    pid->current = 0.0f;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;

    // 设置限幅
    pid->output_limit = output_limit;
    pid->integral_limit = output_limit * 0.5f;

    // 初始化时间和标志
    pid->last_time = system_time_ms;
    pid->first_run = 1;

    return 1;
}

/**
 * @brief PID计算
 */
float pid_calculate(pid_controller_t *pid, float target, float current)
{
    if(pid == NULL) return 0.0f;

    // 更新目标值和当前值
    pid->target = target;
    pid->current = current;

    // 计算误差
    pid->error = target - current;

    // 计算时间间隔
    uint32 current_time = system_time_ms;
    float dt = (float)(current_time - pid->last_time) / 1000.0f;

    // 防止时间间隔异常
    if(dt <= 0.0f || dt > 0.1f) dt = 0.008f;  // 默认8ms

    // 比例项
    float proportional = pid->kp * pid->error;

    // 积分项
    if(!pid->first_run)
    {
        pid->integral += pid->error * dt;
        pid->integral = pid_constrain(pid->integral, -pid->integral_limit, pid->integral_limit);
    }
    float integral = pid->ki * pid->integral;

    // 微分项
    float derivative = 0.0f;
    if(!pid->first_run)
    {
        derivative = pid->kd * (pid->error - pid->last_error) / dt;
    }
    pid->derivative = derivative;

    // 计算输出
    pid->output = proportional + integral + derivative;

    // 输出限幅
    pid->output = pid_constrain(pid->output, -pid->output_limit, pid->output_limit);

    // 更新历史值
    pid->last_error = pid->error;
    pid->last_time = current_time;
    pid->first_run = 0;

    return pid->output;
}

/**
 * @brief 重置PID控制器
 */
uint8 pid_reset(pid_controller_t *pid)
{
    if(pid == NULL) return 0;

    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    pid->last_time = system_time_ms;
    pid->first_run = 1;

    return 1;
}

/**
 * @brief 限幅函数
 */
float pid_constrain(float value, float min_val, float max_val)
{
    if(value > max_val) return max_val;
    if(value < min_val) return min_val;
    return value;
}
