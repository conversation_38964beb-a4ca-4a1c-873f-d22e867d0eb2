#ifndef __ENCODER_DRIVER_H__
#define __ENCODER_DRIVER_H__

#include "zf_common_headfile.h"

//====================================================配置参数====================================================
// 编码器硬件配置
#define ENCODER_LEFT_A_PIN      B0          // 左编码器A相
#define ENCODER_LEFT_B_PIN      B1          // 左编码器B相
#define ENCODER_RIGHT_A_PIN     B2          // 右编码器A相
#define ENCODER_RIGHT_B_PIN     B3          // 右编码器B相

// 编码器参数
#define ENCODER_RESOLUTION      1024        // 编码器分辨率（每转脉冲数）
#define WHEEL_DIAMETER          65.0f       // 轮子直径(mm)
#define SPEED_CALC_PERIOD       100         // 速度计算周期(ms)

//====================================================函数声明====================================================
/**
 * @brief 编码器初始化
 * @return 成功返回1，失败返回0
 */
uint8 encoder_init(void);

/**
 * @brief 读取编码器计数
 */
void encoder_read_counts(void);

/**
 * @brief 计算轮子速度
 */
void encoder_calculate_speed(void);

/**
 * @brief 获取左轮速度
 * @return 左轮速度(mm/s)
 */
float encoder_get_left_speed(void);

/**
 * @brief 获取右轮速度
 * @return 右轮速度(mm/s)
 */
float encoder_get_right_speed(void);

/**
 * @brief 获取编码器计数
 * @param left_count 左编码器计数指针
 * @param right_count 右编码器计数指针
 */
void encoder_get_counts(int32 *left_count, int32 *right_count);

/**
 * @brief 重置编码器计数
 */
void encoder_reset_counts(void);

/**
 * @brief 编码器任务（供调度器调用）
 */
void encoder_task(void);

#endif // __ENCODER_DRIVER_H__
