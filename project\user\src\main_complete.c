#include "zf_common_headfile.h"
#include "../../code/app/line_follow_app.h"
#include "../../code/scheduler/scheduler.h"

//====================================================赛题配置====================================================
// 2025年全国大学生电子设计竞赛E题第一问
// 要求：1.8cm宽黑线寻迹，100cm×100cm正方形轨道，直线跟踪精度<0.5cm

#define SYSTEM_FREQUENCY        80000000    // 系统频率80MHz
#define CONTROL_PERIOD_MS       8           // 控制周期8ms（125Hz高频控制）
#define STARTUP_DELAY_MS        3000        // 启动延时3秒
#define STATUS_LED_PIN          A14         // 状态LED

//====================================================全局变量====================================================
volatile uint32 system_time_ms = 0;                    // 系统时间计数器

//====================================================中断函数====================================================
void pit_handler(void)
{
    system_time_ms++;
}

//====================================================系统初始化====================================================
/**
 * @brief 系统初始化
 */
void system_init(void)
{
    // 基础系统初始化
    clock_init(SYSTEM_FREQUENCY);
    debug_init();
    
    // 初始化状态LED
    gpio_init(STATUS_LED_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
    
    // 初始化系统定时器（1ms中断）
    pit_ms_init(PIT_CH0, 1);
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  2025年电子设计竞赛E题寻迹小车\r\n");
    printf("========================================\r\n");
    printf("目标：1.8cm黑线寻迹，精度<0.5cm\r\n");
    printf("轨道：100cm×100cm正方形\r\n");
    printf("控制：125Hz高频PID控制\r\n");
    printf("平台：MSPM0G3507 + 逐飞开源库\r\n");
    printf("传感器：感为8路灰度传感器\r\n");
    printf("驱动器：TB6612双电机驱动\r\n");
    printf("========================================\r\n");
    printf("系统初始化完成\r\n");
}

//====================================================主函数====================================================
int main(void)
{
    // 系统初始化
    system_init();
    
    // 初始化寻迹应用
    if(!line_follow_app_init())
    {
        printf("❌ 寻迹应用初始化失败！\r\n");
        while(1)
        {
            gpio_toggle_level(STATUS_LED_PIN);
            system_delay_ms(200);
        }
    }
    
    // 初始化调度器
    scheduler_init();
    scheduler_add_task(line_follow_app_task, CONTROL_PERIOD_MS);
    
    printf("✅ 系统初始化完成\r\n");
    printf("等待%d秒后开始寻迹...\r\n", STARTUP_DELAY_MS/1000);
    printf("请将小车放置在黑线起始位置\r\n");
    
    // 启动倒计时
    for(int i = STARTUP_DELAY_MS/1000; i > 0; i--)
    {
        printf("⏰ 倒计时: %d 秒\r\n", i);
        gpio_toggle_level(STATUS_LED_PIN);
        system_delay_ms(1000);
    }
    
    // 开始寻迹
    line_follow_start();
    scheduler_start();
    
    printf("🚀 开始寻迹！目标：完成100cm×100cm正方形轨道\r\n");
    
    // 主循环
    while(1)
    {
        // 运行调度器
        scheduler_run();
        
        // LED状态指示
        static uint32 last_led_time = 0;
        line_follow_status_t status = line_follow_get_status();
        
        uint32 blink_interval = 1000;
        switch(status.state)
        {
            case LINE_FOLLOW_RUNNING:
                blink_interval = 500;   // 快速闪烁表示正常寻迹
                break;
            case LINE_FOLLOW_LINE_LOST:
                blink_interval = 100;   // 极快闪烁表示丢线
                break;
            case LINE_FOLLOW_STOPPED:
                blink_interval = 2000;  // 慢速闪烁表示停止
                break;
            case LINE_FOLLOW_COMPLETED:
                gpio_set_level(STATUS_LED_PIN, GPIO_HIGH);  // 常亮表示完成
                printf("🎉 轨道完成！用时: %d 秒\r\n", status.run_time/1000);
                break;
            default:
                blink_interval = 200;   // 快速闪烁表示错误
                break;
        }
        
        if(status.state != LINE_FOLLOW_COMPLETED && 
           system_time_ms - last_led_time > blink_interval)
        {
            gpio_toggle_level(STATUS_LED_PIN);
            last_led_time = system_time_ms;
        }
        
        // 运行状态监控
        static uint32 last_status_time = 0;
        if(system_time_ms - last_status_time > 5000)  // 每5秒输出一次状态
        {
            if(line_follow_is_running())
            {
                printf("📊 运行状态 - 位置:%.2f, 输出:%.1f, 时间:%ds, 循环:%d\r\n",
                       status.line_position, status.direction_output, 
                       status.run_time/1000, status.control_loop_count);
            }
            last_status_time = system_time_ms;
        }
        
        // 短暂延时
        system_delay_ms(1);
    }
}
