#ifndef __SENSOR_H__
#define __SENSOR_H__

#include "config.h"

//====================================================数据结构====================================================
typedef struct {
    uint8 digital_data;         // 8位数字数据
    uint16 analog_data;         // 模拟数据
    float line_position;        // 线位置 (-4.0 ~ +4.0)
    uint8 line_detected;        // 是否检测到线
    uint32 last_detect_time;    // 上次检测到线的时间
} sensor_data_t;

//====================================================函数声明====================================================
/**
 * @brief 传感器初始化
 * @return 成功返回1，失败返回0
 */
uint8 sensor_init(void);

/**
 * @brief 读取传感器数据
 * @return 传感器数据结构体
 */
sensor_data_t sensor_read(void);

/**
 * @brief 获取线位置
 * @return 线位置 (-4.0表示最左，+4.0表示最右，0表示中心)
 */
float sensor_get_line_position(void);

/**
 * @brief 检查是否检测到线
 * @return 检测到返回1，未检测到返回0
 */
uint8 sensor_is_line_detected(void);

/**
 * @brief 传感器校准
 * @return 成功返回1，失败返回0
 */
uint8 sensor_calibrate(void);

#endif // __SENSOR_H__
