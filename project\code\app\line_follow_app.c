#include "line_follow_app.h"

//====================================================全局变量====================================================
static line_follow_status_t app_status = {0};
static pid_controller_t direction_pid;
static uint8 app_initialized = 0;

//====================================================函数实现====================================================
/**
 * @brief 寻迹应用初始化
 */
uint8 line_follow_app_init(void)
{
    // 初始化传感器
    if(!sensor_init())
    {
        return 0;
    }

    // 初始化电机
    if(!motor_init())
    {
        return 0;
    }

    // 初始化PID控制器
    if(!pid_init(&direction_pid, DIRECTION_KP, DIRECTION_KI, DIRECTION_KD, DIRECTION_OUTPUT_LIMIT))
    {
        return 0;
    }

    // 初始化应用状态
    app_status.state = LINE_FOLLOW_STOPPED;
    app_status.line_position = 0.0f;
    app_status.direction_output = 0.0f;
    app_status.base_speed = MOTOR_BASE_SPEED;
    app_status.line_lost_count = 0;
    app_status.control_loop_count = 0;
    app_status.start_time = 0;
    app_status.run_time = 0;

    app_initialized = 1;
    return 1;
}

/**
 * @brief 开始寻迹
 */
uint8 line_follow_start(void)
{
    if(!app_initialized) return 0;

    app_status.state = LINE_FOLLOW_RUNNING;
    app_status.start_time = system_time_ms;
    app_status.control_loop_count = 0;
    app_status.line_lost_count = 0;

    // 重置PID控制器
    pid_reset(&direction_pid);

    return 1;
}

/**
 * @brief 停止寻迹
 */
uint8 line_follow_stop(void)
{
    if(!app_initialized) return 0;

    app_status.state = LINE_FOLLOW_STOPPED;
    motor_stop_all();

    return 1;
}

/**
 * @brief 寻迹控制主循环
 */
uint8 line_follow_control_loop(void)
{
    if(!app_initialized || app_status.state != LINE_FOLLOW_RUNNING) return 0;

    // 读取传感器数据
    app_status.line_position = sensor_get_line_position();
    uint8 line_detected = sensor_is_line_detected();

    if(line_detected)
    {
        // 检测到线，正常寻迹
        if(app_status.state == LINE_FOLLOW_LINE_LOST)
        {
            app_status.state = LINE_FOLLOW_RUNNING;
        }

        // PID控制计算（目标位置为0，即线的中心）
        app_status.direction_output = pid_calculate(&direction_pid, 0.0f, app_status.line_position);

        // 差速控制
        motor_differential_control(app_status.base_speed, app_status.direction_output);
    }
    else
    {
        // 未检测到线
        app_status.line_lost_count++;

        if(app_status.line_lost_count > LINE_LOST_THRESHOLD)
        {
            app_status.state = LINE_FOLLOW_LINE_LOST;

            // 丢线处理：继续按照最后的方向输出运行
            motor_differential_control(app_status.base_speed / 2, app_status.direction_output);

            // 丢线超时检查
            if(app_status.line_lost_count > LINE_LOST_TIMEOUT)
            {
                line_follow_stop();
                return 0;
            }
        }
    }

    // 更新统计信息
    app_status.control_loop_count++;
    app_status.run_time = system_time_ms - app_status.start_time;

    return 1;
}

/**
 * @brief 获取寻迹状态
 */
line_follow_status_t line_follow_get_status(void)
{
    return app_status;
}

/**
 * @brief 检查是否正在运行
 */
uint8 line_follow_is_running(void)
{
    return (app_status.state == LINE_FOLLOW_RUNNING || app_status.state == LINE_FOLLOW_LINE_LOST);
}

/**
 * @brief 设置基础速度
 */
uint8 line_follow_set_base_speed(int16 speed)
{
    if(!app_initialized) return 0;

    if(speed < 10) speed = 10;
    if(speed > MOTOR_MAX_SPEED) speed = MOTOR_MAX_SPEED;

    app_status.base_speed = speed;
    return 1;
}

/**
 * @brief 应用层任务（供调度器调用）
 */
void line_follow_app_task(void)
{
    if(line_follow_is_running())
    {
        line_follow_control_loop();
    }
}
