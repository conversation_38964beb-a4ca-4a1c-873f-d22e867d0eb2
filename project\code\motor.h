#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "config.h"

//====================================================数据结构====================================================
typedef enum {
    MOTOR_LEFT = 0,
    MOTOR_RIGHT = 1,
    MOTOR_BOTH = 2
} motor_id_t;

typedef enum {
    MOTOR_FORWARD = 0,
    MOTOR_BACKWARD = 1,
    MOTOR_STOP = 2
} motor_direction_t;

typedef struct {
    int16 speed;                // 当前速度 (-100 ~ +100)
    motor_direction_t direction; // 当前方向
    uint8 enabled;              // 是否使能
} motor_status_t;

//====================================================函数声明====================================================
/**
 * @brief 电机初始化
 * @return 成功返回1，失败返回0
 */
uint8 motor_init(void);

/**
 * @brief 设置电机速度
 * @param motor_id 电机ID
 * @param speed 速度 (-100 ~ +100)
 * @return 成功返回1，失败返回0
 */
uint8 motor_set_speed(motor_id_t motor_id, int16 speed);

/**
 * @brief 差速控制（寻迹专用）
 * @param base_speed 基础速度
 * @param direction_output 方向控制输出
 * @return 成功返回1，失败返回0
 */
uint8 motor_differential_control(int16 base_speed, float direction_output);

/**
 * @brief 停止所有电机
 * @return 成功返回1，失败返回0
 */
uint8 motor_stop_all(void);

/**
 * @brief 使能电机
 * @return 成功返回1，失败返回0
 */
uint8 motor_enable(void);

/**
 * @brief 禁用电机
 * @return 成功返回1，失败返回0
 */
uint8 motor_disable(void);

/**
 * @brief 获取电机状态
 * @param motor_id 电机ID
 * @return 电机状态结构体
 */
motor_status_t motor_get_status(motor_id_t motor_id);

#endif // __MOTOR_H__
