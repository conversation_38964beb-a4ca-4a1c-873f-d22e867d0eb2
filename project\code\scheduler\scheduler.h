#ifndef __SCHEDULER_H__
#define __SCHEDULER_H__

#include "zf_common_headfile.h"

//====================================================配置参数====================================================
#define MAX_TASKS               10          // 最大任务数量

// 外部变量声明
extern volatile uint32 system_time_ms;

//====================================================数据结构====================================================
typedef void (*task_func_t)(void);

typedef struct {
    task_func_t task_func;      // 任务函数指针
    uint32 period_ms;           // 任务周期（毫秒）
    uint32 last_run_time;       // 上次运行时间
    uint8 enabled;              // 任务使能标志
} scheduler_task_t;

//====================================================函数声明====================================================
/**
 * @brief 调度器初始化
 * @return 成功返回1，失败返回0
 */
uint8 scheduler_init(void);

/**
 * @brief 添加任务
 * @param task_func 任务函数指针
 * @param period_ms 任务周期（毫秒）
 * @return 成功返回1，失败返回0
 */
uint8 scheduler_add_task(task_func_t task_func, uint32 period_ms);

/**
 * @brief 启动调度器
 * @return 成功返回1，失败返回0
 */
uint8 scheduler_start(void);

/**
 * @brief 停止调度器
 * @return 成功返回1，失败返回0
 */
uint8 scheduler_stop(void);

/**
 * @brief 调度器运行（在主循环中调用）
 */
void scheduler_run(void);

#endif // __SCHEDULER_H__
