#include "zf_common_headfile.h"

//====================================================配置参数====================================================
// 编码器硬件配置
#define ENCODER_LEFT_A_PIN      B0          // 左编码器A相
#define ENCODER_LEFT_B_PIN      B1          // 左编码器B相
#define ENCODER_RIGHT_A_PIN     B2          // 右编码器A相
#define ENCODER_RIGHT_B_PIN     B3          // 右编码器B相

// 编码器参数
#define ENCODER_RESOLUTION      1024        // 编码器分辨率（每转脉冲数）
#define WHEEL_DIAMETER          65.0f       // 轮子直径(mm)
#define SPEED_CALC_PERIOD       100         // 速度计算周期(ms)

//====================================================全局变量====================================================
static int32 left_encoder_count = 0;       // 左编码器计数
static int32 right_encoder_count = 0;      // 右编码器计数
static int32 left_last_count = 0;          // 左编码器上次计数
static int32 right_last_count = 0;         // 右编码器上次计数
static float left_speed = 0.0f;            // 左轮速度(mm/s)
static float right_speed = 0.0f;           // 右轮速度(mm/s)
static uint32 last_calc_time = 0;          // 上次计算时间
static uint8 encoder_initialized = 0;      // 初始化标志

//====================================================函数实现====================================================
/**
 * @brief 编码器初始化
 */
uint8 encoder_init(void)
{
    // 初始化编码器引脚为输入模式
    gpio_init(ENCODER_LEFT_A_PIN, GPI, GPIO_LOW, GPI_PULL_UP);
    gpio_init(ENCODER_LEFT_B_PIN, GPI, GPIO_LOW, GPI_PULL_UP);
    gpio_init(ENCODER_RIGHT_A_PIN, GPI, GPIO_LOW, GPI_PULL_UP);
    gpio_init(ENCODER_RIGHT_B_PIN, GPI, GPIO_LOW, GPI_PULL_UP);

    // 初始化变量
    left_encoder_count = 0;
    right_encoder_count = 0;
    left_last_count = 0;
    right_last_count = 0;
    left_speed = 0.0f;
    right_speed = 0.0f;
    last_calc_time = system_time_ms;

    encoder_initialized = 1;
    return 1;
}

/**
 * @brief 读取编码器计数（简化实现）
 */
void encoder_read_counts(void)
{
    if(!encoder_initialized) return;

    // 简化实现：基于电机PWM估算编码器计数
    // 实际项目中应该使用硬件编码器中断
    // 这里仅作为占位实现，确保系统能够编译运行

    // 模拟编码器计数增加（基于时间）
    uint32 current_time = system_time_ms;
    uint32 time_diff = current_time - last_calc_time;

    if(time_diff >= SPEED_CALC_PERIOD)
    {
        // 简单的模拟计数（实际应该从硬件读取）
        left_encoder_count += 10;
        right_encoder_count += 10;

        last_calc_time = current_time;
    }
}

/**
 * @brief 计算轮子速度
 */
void encoder_calculate_speed(void)
{
    if(!encoder_initialized) return;

    uint32 current_time = system_time_ms;
    uint32 time_diff = current_time - last_calc_time;

    if(time_diff >= SPEED_CALC_PERIOD)
    {
        // 计算编码器计数差值
        int32 left_diff = left_encoder_count - left_last_count;
        int32 right_diff = right_encoder_count - right_last_count;

        // 计算速度 (mm/s)
        float time_s = (float)time_diff / 1000.0f;
        float distance_per_pulse = (3.14159f * WHEEL_DIAMETER) / ENCODER_RESOLUTION;

        left_speed = (left_diff * distance_per_pulse) / time_s;
        right_speed = (right_diff * distance_per_pulse) / time_s;

        // 更新上次计数
        left_last_count = left_encoder_count;
        right_last_count = right_encoder_count;
        last_calc_time = current_time;
    }
}

/**
 * @brief 获取左轮速度
 */
float encoder_get_left_speed(void)
{
    return left_speed;
}

/**
 * @brief 获取右轮速度
 */
float encoder_get_right_speed(void)
{
    return right_speed;
}

/**
 * @brief 获取编码器计数
 */
void encoder_get_counts(int32 *left_count, int32 *right_count)
{
    if(left_count) *left_count = left_encoder_count;
    if(right_count) *right_count = right_encoder_count;
}

/**
 * @brief 重置编码器计数
 */
void encoder_reset_counts(void)
{
    left_encoder_count = 0;
    right_encoder_count = 0;
    left_last_count = 0;
    right_last_count = 0;
    left_speed = 0.0f;
    right_speed = 0.0f;
}

/**
 * @brief 编码器任务（供调度器调用）
 */
void encoder_task(void)
{
    if(encoder_initialized)
    {
        encoder_read_counts();
        encoder_calculate_speed();
    }
}
