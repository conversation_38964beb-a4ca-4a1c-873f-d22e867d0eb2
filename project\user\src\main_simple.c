#include "zf_common_headfile.h"
#include "../../simple_version/config.h"
#include "../../simple_version/sensor.h"
#include "../../simple_version/motor.h"
#include "../../simple_version/pid.h"

//====================================================全局变量====================================================
volatile uint32 system_time_ms = 0;        // 系统时间计数器
system_state_t system_state = SYSTEM_INIT; // 系统状态

// PID控制器
static pid_controller_t direction_pid;      // 方向控制PID

// 系统运行数据
static float current_line_position = 0.0f;
static float direction_output = 0.0f;
static uint32 line_lost_start_time = 0;
static uint32 last_control_time = 0;

//====================================================中断服务函数====================================================
/**
 * @brief 系统时钟中断（1ms）
 */
void pit_handler(void)
{
    system_time_ms++;
}

//====================================================内部函数====================================================
/**
 * @brief 系统初始化
 */
static uint8 system_init_simple(void)
{
    // 基础系统初始化
    clock_init(SYSTEM_FREQUENCY);
    debug_init();
    
    // 初始化状态LED
    gpio_init(STATUS_LED_PIN, GPO, GPIO_LOW, GPO_PUSH_PULL);
    
    // 初始化系统定时器（1ms中断）
    pit_ms_init(PIT_CH0, 1);
    
    // 初始化各模块
    if(!sensor_init())
    {
        DEBUG_PRINT("传感器初始化失败\r\n");
        return 0;
    }
    
    if(!motor_init())
    {
        DEBUG_PRINT("电机初始化失败\r\n");
        return 0;
    }
    
    // 初始化方向PID控制器
    if(!pid_init(&direction_pid, DIRECTION_KP, DIRECTION_KI, DIRECTION_KD, DIRECTION_OUTPUT_LIMIT))
    {
        DEBUG_PRINT("PID初始化失败\r\n");
        return 0;
    }
    
    DEBUG_PRINT("系统初始化完成\r\n");
    return 1;
}

/**
 * @brief LED状态指示
 */
static void update_status_led(void)
{
    static uint32 last_led_time = 0;
    static uint8 led_state = 0;
    
    uint32 blink_interval = 1000;  // 默认1秒闪烁
    
    switch(system_state)
    {
        case SYSTEM_INIT:
            blink_interval = 200;   // 快速闪烁
            break;
        case SYSTEM_READY:
            blink_interval = 500;   // 中速闪烁
            break;
        case SYSTEM_RUNNING:
            blink_interval = 1000;  // 慢速闪烁
            break;
        case SYSTEM_LINE_LOST:
            blink_interval = 100;   // 非常快速闪烁
            break;
        case SYSTEM_ERROR:
            gpio_set_level(STATUS_LED_PIN, GPIO_HIGH);  // 常亮
            return;
    }
    
    if(system_time_ms - last_led_time >= blink_interval)
    {
        led_state = !led_state;
        gpio_set_level(STATUS_LED_PIN, led_state ? GPIO_HIGH : GPIO_LOW);
        last_led_time = system_time_ms;
    }
}

/**
 * @brief 寻迹控制主循环
 */
static void line_follow_control(void)
{
    // 控制周期检查
    if(system_time_ms - last_control_time < CONTROL_PERIOD_MS)
    {
        return;
    }
    last_control_time = system_time_ms;
    
    // 读取传感器数据
    sensor_data_t sensor_data = sensor_read();
    current_line_position = sensor_data.line_position;
    
    // 检查是否检测到线
    if(sensor_data.line_detected)
    {
        // 检测到线，正常寻迹
        if(system_state == SYSTEM_LINE_LOST)
        {
            DEBUG_PRINT("重新检测到线\r\n");
        }
        system_state = SYSTEM_RUNNING;
        
        // PID控制计算（目标位置为0，即线的中心）
        direction_output = pid_calculate(&direction_pid, 0.0f, current_line_position);
        
        // 差速控制
        motor_differential_control(MOTOR_BASE_SPEED, direction_output);
    }
    else
    {
        // 未检测到线
        if(system_state == SYSTEM_RUNNING)
        {
            // 刚刚丢线，记录时间
            line_lost_start_time = system_time_ms;
            system_state = SYSTEM_LINE_LOST;
            DEBUG_PRINT("丢线检测\r\n");
        }
        
        // 检查丢线超时
        if(system_time_ms - line_lost_start_time > LINE_LOST_TIMEOUT)
        {
            // 丢线超时，停止运行
            motor_stop_all();
            system_state = SYSTEM_ERROR;
            DEBUG_PRINT("丢线超时，停止运行\r\n");
            return;
        }
        
        // 丢线处理：继续按照最后的方向输出运行一小段时间
        motor_differential_control(MOTOR_BASE_SPEED / 2, direction_output);
    }
}

//====================================================主函数====================================================
int main(void)
{
    // 系统初始化
    if(!system_init_simple())
    {
        system_state = SYSTEM_ERROR;
        while(1)
        {
            gpio_toggle_level(STATUS_LED_PIN);
            system_delay_ms(100);
        }
    }
    
    system_state = SYSTEM_READY;
    DEBUG_PRINT("寻迹小车系统启动完成\r\n");
    DEBUG_PRINT("等待%d秒后开始寻迹...\r\n", STARTUP_DELAY_MS/1000);
    
    // 启动延时
    uint32 startup_time = system_time_ms;
    while(system_time_ms - startup_time < STARTUP_DELAY_MS)
    {
        update_status_led();
        system_delay_ms(10);
    }
    
    // 可选：传感器校准
    DEBUG_PRINT("开始传感器校准...\r\n");
    sensor_calibrate();
    
    // 使能电机
    motor_enable();
    
    DEBUG_PRINT("开始寻迹！\r\n");
    system_state = SYSTEM_RUNNING;
    
    // 主循环
    while(1)
    {
        // 更新LED状态
        update_status_led();
        
        // 寻迹控制
        if(system_state == SYSTEM_RUNNING || system_state == SYSTEM_LINE_LOST)
        {
            line_follow_control();
        }
        
        // 短暂延时
        system_delay_ms(1);
    }
}
