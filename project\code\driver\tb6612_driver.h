#ifndef __TB6612_DRIVER_H__
#define __TB6612_DRIVER_H__

#include "zf_common_headfile.h"

//====================================================配置参数====================================================
// 硬件引脚配置
#define MOTOR_LEFT_PWM          PWM_CH2     // 左电机PWM
#define MOTOR_RIGHT_PWM         PWM_CH3     // 右电机PWM
#define MOTOR_LEFT_DIR1         A16         // 左电机方向1
#define MOTOR_LEFT_DIR2         A17         // 左电机方向2
#define MOTOR_RIGHT_DIR1        A18         // 右电机方向1
#define MOTOR_RIGHT_DIR2        A19         // 右电机方向2
#define MOTOR_ENABLE_PIN        A20         // 电机使能

// 电机参数
#define MOTOR_PWM_FREQUENCY     20000       // PWM频率20kHz
#define MOTOR_BASE_SPEED        45          // 基础速度
#define MOTOR_MAX_SPEED         80          // 最大速度
#define MOTOR_DEAD_ZONE         12          // 死区补偿

//====================================================数据结构====================================================
typedef enum {
    MOTOR_LEFT = 0,
    MOTOR_RIGHT = 1,
    MOTOR_BOTH = 2
} motor_id_t;

typedef enum {
    MOTOR_FORWARD = 0,
    MOTOR_BACKWARD = 1,
    MOTOR_STOP = 2
} motor_direction_t;

typedef struct {
    int16 speed;                // 当前速度 (-100 ~ +100)
    motor_direction_t direction; // 当前方向
    uint8 enabled;              // 是否使能
} motor_status_t;

//====================================================函数声明====================================================
/**
 * @brief 电机初始化
 * @return 成功返回1，失败返回0
 */
uint8 motor_init(void);

/**
 * @brief 设置电机速度
 * @param motor_id 电机ID
 * @param speed 速度 (-100 ~ +100)
 * @return 成功返回1，失败返回0
 */
uint8 motor_set_speed(motor_id_t motor_id, int16 speed);

/**
 * @brief 差速控制（寻迹专用）
 * @param base_speed 基础速度
 * @param direction_output 方向控制输出
 * @return 成功返回1，失败返回0
 */
uint8 motor_differential_control(int16 base_speed, float direction_output);

/**
 * @brief 停止所有电机
 * @return 成功返回1，失败返回0
 */
uint8 motor_stop_all(void);

/**
 * @brief 获取电机状态
 * @param motor_id 电机ID
 * @return 电机状态结构体
 */
motor_status_t motor_get_status(motor_id_t motor_id);

#endif // __TB6612_DRIVER_H__
