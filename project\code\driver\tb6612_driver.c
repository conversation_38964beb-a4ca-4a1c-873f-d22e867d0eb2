#include "tb6612_driver.h"

//====================================================全局变量====================================================
static motor_status_t left_motor = {0};
static motor_status_t right_motor = {0};
static uint8 motor_initialized = 0;

//====================================================内部函数====================================================
/**
 * @brief 速度限幅
 */
static int16 motor_constrain_speed(int16 speed)
{
    if(speed > MOTOR_MAX_SPEED) return MOTOR_MAX_SPEED;
    if(speed < -MOTOR_MAX_SPEED) return -MOTOR_MAX_SPEED;
    return speed;
}

/**
 * @brief 死区补偿
 */
static int16 motor_dead_zone_compensation(int16 speed)
{
    if(speed == 0) return 0;

    if(speed > 0)
    {
        return speed + MOTOR_DEAD_ZONE;
    }
    else
    {
        return speed - MOTOR_DEAD_ZONE;
    }
}

/**
 * @brief 设置单个电机PWM和方向
 */
static void motor_set_single(motor_id_t motor_id, int16 speed)
{
    speed = motor_constrain_speed(speed);
    speed = motor_dead_zone_compensation(speed);

    uint32 duty = (uint32)abs(speed);
    if(duty > 100) duty = 100;

    if(motor_id == MOTOR_LEFT)
    {
        pwm_set_duty(MOTOR_LEFT_PWM, duty);

        if(speed > 0)
        {
            gpio_set_level(MOTOR_LEFT_DIR1, GPIO_HIGH);
            gpio_set_level(MOTOR_LEFT_DIR2, GPIO_LOW);
            left_motor.direction = MOTOR_FORWARD;
        }
        else if(speed < 0)
        {
            gpio_set_level(MOTOR_LEFT_DIR1, GPIO_LOW);
            gpio_set_level(MOTOR_LEFT_DIR2, GPIO_HIGH);
            left_motor.direction = MOTOR_BACKWARD;
        }
        else
        {
            gpio_set_level(MOTOR_LEFT_DIR1, GPIO_LOW);
            gpio_set_level(MOTOR_LEFT_DIR2, GPIO_LOW);
            left_motor.direction = MOTOR_STOP;
        }
        left_motor.speed = speed;
    }
    else if(motor_id == MOTOR_RIGHT)
    {
        pwm_set_duty(MOTOR_RIGHT_PWM, duty);

        if(speed > 0)
        {
            gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_HIGH);
            gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_LOW);
            right_motor.direction = MOTOR_FORWARD;
        }
        else if(speed < 0)
        {
            gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_LOW);
            gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_HIGH);
            right_motor.direction = MOTOR_BACKWARD;
        }
        else
        {
            gpio_set_level(MOTOR_RIGHT_DIR1, GPIO_LOW);
            gpio_set_level(MOTOR_RIGHT_DIR2, GPIO_LOW);
            right_motor.direction = MOTOR_STOP;
        }
        right_motor.speed = speed;
    }
}

//====================================================外部函数====================================================
/**
 * @brief 电机初始化
 */
uint8 motor_init(void)
{
    // 初始化PWM
    pwm_init(MOTOR_LEFT_PWM, MOTOR_PWM_FREQUENCY, 0);
    pwm_init(MOTOR_RIGHT_PWM, MOTOR_PWM_FREQUENCY, 0);

    // 初始化方向控制引脚
    gpio_init(MOTOR_LEFT_DIR1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_LEFT_DIR2, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_RIGHT_DIR1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(MOTOR_RIGHT_DIR2, GPO, GPIO_LOW, GPO_PUSH_PULL);

    // 初始化使能引脚
    gpio_init(MOTOR_ENABLE_PIN, GPO, GPIO_HIGH, GPO_PUSH_PULL);

    // 停止所有电机
    motor_stop_all();

    motor_initialized = 1;
    return 1;
}

/**
 * @brief 设置电机速度
 */
uint8 motor_set_speed(motor_id_t motor_id, int16 speed)
{
    if(!motor_initialized) return 0;

    if(motor_id == MOTOR_BOTH)
    {
        motor_set_single(MOTOR_LEFT, speed);
        motor_set_single(MOTOR_RIGHT, speed);
    }
    else
    {
        motor_set_single(motor_id, speed);
    }

    return 1;
}

/**
 * @brief 差速控制（寻迹专用）
 */
uint8 motor_differential_control(int16 base_speed, float direction_output)
{
    if(!motor_initialized) return 0;

    // 计算左右轮速度
    int16 left_speed = base_speed - (int16)direction_output;
    int16 right_speed = base_speed + (int16)direction_output;

    // 设置电机速度
    motor_set_single(MOTOR_LEFT, left_speed);
    motor_set_single(MOTOR_RIGHT, right_speed);

    return 1;
}

/**
 * @brief 停止所有电机
 */
uint8 motor_stop_all(void)
{
    if(!motor_initialized) return 0;

    motor_set_single(MOTOR_LEFT, 0);
    motor_set_single(MOTOR_RIGHT, 0);
    return 1;
}

/**
 * @brief 获取电机状态
 */
motor_status_t motor_get_status(motor_id_t motor_id)
{
    if(motor_id == MOTOR_LEFT)
    {
        return left_motor;
    }
    else
    {
        return right_motor;
    }
}
