/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : C:\Users\<USER>\Desktop\SeekFree_MSPM0G3507_Opensource_Library\SeekFree_MSPM0G3507_Opensource_Library\project\keil\SeekFree_MSPM0G3507_Device_Library_Sequences_0000.log
 *  Created     : 21:08:10 (01/08/2025)
 *  Device      : MSPM0G3507
 *  PDSC File   : C:/keil5/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1_tmp/TexasInstruments.MSPM0G1X0X_G3X0X_DFP.pdsc
 *
 */

[21:08:10.998]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[21:08:10.998]  
[21:08:10.998]  <debugvars>
[21:08:10.998]    // Pre-defined
[21:08:10.998]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[21:08:10.998]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[21:08:10.998]    __dp=0x00000000
[21:08:10.998]    __ap=0x00000000
[21:08:10.998]    __traceout=0x00000000      (Trace Disabled)
[21:08:11.000]    __errorcontrol=0x00000000  (Skip Errors="False")
[21:08:11.000]    __FlashAddr=0x00000000
[21:08:11.000]    __FlashLen=0x00000000
[21:08:11.000]    __FlashArg=0x00000000
[21:08:11.001]    __FlashOp=0x00000000
[21:08:11.001]    __Result=0x00000000
[21:08:11.001]  </debugvars>
[21:08:11.001]  
[21:08:11.001]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[21:08:11.001]    <block atomic="false" info="">
[21:08:11.001]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[21:08:11.001]        // -> [isSWJ <= 0x00000001]
[21:08:11.001]      __var hasDormant = __protocol & 0x00020000;
[21:08:11.001]        // -> [hasDormant <= 0x00000000]
[21:08:11.002]      __var protType   = __protocol & 0x0000FFFF;
[21:08:11.002]        // -> [protType <= 0x00000002]
[21:08:11.002]    </block>
[21:08:11.002]    <control if="protType == 1" while="" timeout="0" info="">
[21:08:11.002]      // if-block "protType == 1"
[21:08:11.002]        // =>  FALSE
[21:08:11.002]      // skip if-block "protType == 1"
[21:08:11.002]    </control>
[21:08:11.002]    <control if="protType == 2" while="" timeout="0" info="">
[21:08:11.002]      // if-block "protType == 2"
[21:08:11.002]        // =>  TRUE
[21:08:11.002]      <control if="isSWJ" while="" timeout="0" info="">
[21:08:11.002]        // if-block "isSWJ"
[21:08:11.002]          // =>  TRUE
[21:08:11.003]        <control if="hasDormant" while="" timeout="0" info="">
[21:08:11.003]          // if-block "hasDormant"
[21:08:11.003]            // =>  FALSE
[21:08:11.003]          // skip if-block "hasDormant"
[21:08:11.003]        </control>
[21:08:11.003]        <control if="!hasDormant" while="" timeout="0" info="">
[21:08:11.003]          // if-block "!hasDormant"
[21:08:11.003]            // =>  TRUE
[21:08:11.003]          <block atomic="false" info="">
[21:08:11.003]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[21:08:11.006]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:11.006]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[21:08:11.008]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[21:08:11.009]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[21:08:11.010]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:11.010]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[21:08:11.012]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[21:08:11.012]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[21:08:11.014]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:11.014]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[21:08:11.015]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[21:08:11.016]          </block>
[21:08:11.016]          // end if-block "!hasDormant"
[21:08:11.016]        </control>
[21:08:11.016]        // end if-block "isSWJ"
[21:08:11.016]      </control>
[21:08:11.016]      <control if="!isSWJ" while="" timeout="0" info="">
[21:08:11.016]        // if-block "!isSWJ"
[21:08:11.016]          // =>  FALSE
[21:08:11.016]        // skip if-block "!isSWJ"
[21:08:11.016]      </control>
[21:08:11.016]      <block atomic="false" info="">
[21:08:11.016]        ReadDP(0x0);
[21:08:11.018]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[21:08:11.018]      </block>
[21:08:11.018]      // end if-block "protType == 2"
[21:08:11.020]    </control>
[21:08:11.020]  </sequence>
[21:08:11.020]  
[21:08:16.319]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[21:08:16.319]  
[21:08:16.320]  <debugvars>
[21:08:16.320]    // Pre-defined
[21:08:16.320]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[21:08:16.320]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[21:08:16.320]    __dp=0x00000000
[21:08:16.320]    __ap=0x00000000
[21:08:16.320]    __traceout=0x00000000      (Trace Disabled)
[21:08:16.320]    __errorcontrol=0x00000000  (Skip Errors="False")
[21:08:16.320]    __FlashAddr=0x00000000
[21:08:16.320]    __FlashLen=0x00000000
[21:08:16.320]    __FlashArg=0x00000000
[21:08:16.320]    __FlashOp=0x00000000
[21:08:16.320]    __Result=0x00000000
[21:08:16.322]  </debugvars>
[21:08:16.322]  
[21:08:16.322]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[21:08:16.322]    <block atomic="false" info="">
[21:08:16.322]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[21:08:16.322]        // -> [isSWJ <= 0x00000001]
[21:08:16.322]      __var hasDormant = __protocol & 0x00020000;
[21:08:16.322]        // -> [hasDormant <= 0x00000000]
[21:08:16.322]      __var protType   = __protocol & 0x0000FFFF;
[21:08:16.322]        // -> [protType <= 0x00000002]
[21:08:16.322]    </block>
[21:08:16.322]    <control if="protType == 1" while="" timeout="0" info="">
[21:08:16.322]      // if-block "protType == 1"
[21:08:16.322]        // =>  FALSE
[21:08:16.322]      // skip if-block "protType == 1"
[21:08:16.322]    </control>
[21:08:16.323]    <control if="protType == 2" while="" timeout="0" info="">
[21:08:16.323]      // if-block "protType == 2"
[21:08:16.323]        // =>  TRUE
[21:08:16.323]      <control if="isSWJ" while="" timeout="0" info="">
[21:08:16.323]        // if-block "isSWJ"
[21:08:16.323]          // =>  TRUE
[21:08:16.323]        <control if="hasDormant" while="" timeout="0" info="">
[21:08:16.323]          // if-block "hasDormant"
[21:08:16.323]            // =>  FALSE
[21:08:16.323]          // skip if-block "hasDormant"
[21:08:16.323]        </control>
[21:08:16.323]        <control if="!hasDormant" while="" timeout="0" info="">
[21:08:16.323]          // if-block "!hasDormant"
[21:08:16.323]            // =>  TRUE
[21:08:16.323]          <block atomic="false" info="">
[21:08:16.323]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[21:08:16.326]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:16.326]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[21:08:16.327]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[21:08:16.327]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[21:08:16.329]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:16.329]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[21:08:16.332]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[21:08:16.332]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[21:08:16.334]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[21:08:16.334]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[21:08:16.336]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[21:08:16.336]          </block>
[21:08:16.336]          // end if-block "!hasDormant"
[21:08:16.336]        </control>
[21:08:16.336]        // end if-block "isSWJ"
[21:08:16.336]      </control>
[21:08:16.336]      <control if="!isSWJ" while="" timeout="0" info="">
[21:08:16.336]        // if-block "!isSWJ"
[21:08:16.336]          // =>  FALSE
[21:08:16.336]        // skip if-block "!isSWJ"
[21:08:16.336]      </control>
[21:08:16.336]      <block atomic="false" info="">
[21:08:16.336]        ReadDP(0x0);
[21:08:16.338]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[21:08:16.338]      </block>
[21:08:16.338]      // end if-block "protType == 2"
[21:08:16.339]    </control>
[21:08:16.339]  </sequence>
[21:08:16.339]  
[21:08:16.344]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[21:08:16.344]  
[21:08:16.344]  <debugvars>
[21:08:16.344]    // Pre-defined
[21:08:16.344]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[21:08:16.344]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[21:08:16.344]    __dp=0x00000000
[21:08:16.344]    __ap=0x00000000
[21:08:16.344]    __traceout=0x00000000      (Trace Disabled)
[21:08:16.344]    __errorcontrol=0x00000000  (Skip Errors="False")
[21:08:16.344]    __FlashAddr=0x00000000
[21:08:16.344]    __FlashLen=0x00000000
[21:08:16.344]    __FlashArg=0x00000000
[21:08:16.344]    __FlashOp=0x00000000
[21:08:16.345]    __Result=0x00000000
[21:08:16.345]  </debugvars>
[21:08:16.345]  
[21:08:16.345]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[21:08:16.345]    <block atomic="false" info="">
[21:08:16.345]      __var SW_DP_ABORT       = 0x0;
[21:08:16.345]        // -> [SW_DP_ABORT <= 0x00000000]
[21:08:16.345]      __var DP_CTRL_STAT      = 0x4;
[21:08:16.345]        // -> [DP_CTRL_STAT <= 0x00000004]
[21:08:16.345]      __var DP_SELECT         = 0x8;
[21:08:16.345]        // -> [DP_SELECT <= 0x00000008]
[21:08:16.345]      __var powered_down      = 0;
[21:08:16.345]        // -> [powered_down <= 0x00000000]
[21:08:16.345]      WriteDP(DP_SELECT, 0x00000000);
[21:08:16.347]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[21:08:16.347]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[21:08:16.350]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[21:08:16.350]        // -> [powered_down <= 0x00000001]
[21:08:16.350]    </block>
[21:08:16.350]    <control if="powered_down" while="" timeout="0" info="">
[21:08:16.350]      // if-block "powered_down"
[21:08:16.350]        // =>  TRUE
[21:08:16.350]      <block atomic="false" info="">
[21:08:16.350]        Message(0, "Debug/System power-up request sent");
[21:08:16.352]        WriteDP(DP_CTRL_STAT, 0x50000000);
[21:08:16.354]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[21:08:16.354]      </block>
[21:08:16.354]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[21:08:16.354]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[21:08:16.355]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[21:08:16.355]        // while-condition  =>  FALSE
[21:08:16.355]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[21:08:16.355]      </control>
[21:08:16.355]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[21:08:16.355]        // if-block "(__protocol & 0xFFFF) == 1"
[21:08:16.355]          // =>  FALSE
[21:08:16.357]        // skip if-block "(__protocol & 0xFFFF) == 1"
[21:08:16.357]      </control>
[21:08:16.357]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[21:08:16.357]        // if-block "(__protocol & 0xFFFF) == 2"
[21:08:16.357]          // =>  TRUE
[21:08:16.357]        <block atomic="false" info="">
[21:08:16.357]          Message(0, "executing SWD power up");
[21:08:16.358]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[21:08:16.360]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[21:08:16.360]          WriteDP(SW_DP_ABORT, 0x0000001E);
[21:08:16.361]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[21:08:16.361]        </block>
[21:08:16.361]        // end if-block "(__protocol & 0xFFFF) == 2"
[21:08:16.362]      </control>
[21:08:16.362]      // end if-block "powered_down"
[21:08:16.362]    </control>
[21:08:16.362]    <block atomic="false" info="">
[21:08:16.362]      __var DEBUG_PORT_VAL    = 0;
[21:08:16.362]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[21:08:16.362]      __var ACCESS_POINT_VAL  = 0;
[21:08:16.362]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[21:08:16.362]      __ap = 1; 
[21:08:16.362]        // -> [__ap <= 0x00000001]
[21:08:16.362]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[21:08:16.367]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[21:08:16.367]      __ap = 4;
[21:08:16.367]        // -> [__ap <= 0x00000004]
[21:08:16.367]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[21:08:16.370]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[21:08:16.370]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[21:08:16.370]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[21:08:16.372]    </block>
[21:08:16.372]    <block atomic="false" info="">
[21:08:16.372]      __var nReset = 0x80;
[21:08:16.372]        // -> [nReset <= 0x00000080]
[21:08:16.373]      __var canReadPins = 0;
[21:08:16.373]        // -> [canReadPins <= 0x00000000]
[21:08:16.373]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[21:08:16.375]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[21:08:16.375]        // -> [canReadPins <= 0x00000001]
[21:08:16.375]    </block>
[21:08:16.375]    <control if="" while="1" timeout="200" info="">
[21:08:16.375]      // while "1"  (timeout="200")
[21:08:16.375]      // while-condition  =>  TRUE
[21:08:16.375]      // while "1"  (timeout="200")
[21:08:16.375]      // while-condition  =>  TRUE
[21:08:16.375]      // while "1"  (timeout="200")
[21:08:16.375]      // while-condition  =>  TRUE
[21:08:16.375]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.376]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.376]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.376]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.376]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.376]      // while "1"  (timeout="200")
[21:08:16.376]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.377]      // while-condition  =>  TRUE
[21:08:16.377]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.379]      // while-condition  =>  TRUE
[21:08:16.379]      // while "1"  (timeout="200")
[21:08:16.380]      // while-condition  =>  TRUE
[21:08:16.380]      // while "1"  (timeout="200")
[21:08:16.380]      // while-condition  =>  TRUE
[21:08:16.380]      // while "1"  (timeout="200")
[21:08:16.380]      // while-condition  =>  TRUE
[21:08:16.380]      // while "1"  (timeout="200")
[21:08:16.380]      // while-condition  =>  TRUE
[21:08:16.380]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.381]      // while "1"  (timeout="200")
[21:08:16.381]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.382]      // while "1"  (timeout="200")
[21:08:16.382]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.383]      // while "1"  (timeout="200")
[21:08:16.383]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.384]      // while "1"  (timeout="200")
[21:08:16.384]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.385]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.385]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.385]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.385]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.385]      // while-condition  =>  TRUE
[21:08:16.385]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.387]      // while-condition  =>  TRUE
[21:08:16.387]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.388]      // while-condition  =>  TRUE
[21:08:16.388]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.389]      // while "1"  (timeout="200")
[21:08:16.389]      // while-condition  =>  TRUE
[21:08:16.390]      // while "1"  (timeout="200")
[21:08:16.390]      // while  =>  TIMEOUT
[21:08:16.390]      // end while "1"
[21:08:16.390]    </control>
[21:08:16.390]    <control if="canReadPins" while="" timeout="0" info="">
[21:08:16.390]      // if-block "canReadPins"
[21:08:16.390]        // =>  TRUE
[21:08:16.390]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[21:08:16.390]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[21:08:16.392]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[21:08:16.392]        // while-condition  =>  TRUE
[21:08:16.392]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[21:08:16.394]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[21:08:16.394]        // while-condition  =>  FALSE
[21:08:16.394]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[21:08:16.394]      </control>
[21:08:16.394]      // end if-block "canReadPins"
[21:08:16.394]    </control>
[21:08:16.394]    <control if="!canReadPins" while="" timeout="0" info="">
[21:08:16.394]      // if-block "!canReadPins"
[21:08:16.394]        // =>  FALSE
[21:08:16.394]      // skip if-block "!canReadPins"
[21:08:16.394]    </control>
[21:08:16.394]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[21:08:16.394]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[21:08:16.395]        // =>  TRUE
[21:08:16.395]      <block atomic="false" info="">
[21:08:16.395]        WriteAP(0x00, 0x190008);
[21:08:16.398]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[21:08:16.398]        WriteAP(0xF0, 0x01);
[21:08:16.402]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[21:08:16.402]      </block>
[21:08:16.402]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[21:08:16.402]    </control>
[21:08:16.402]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[21:08:16.402]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[21:08:16.402]        // =>  FALSE
[21:08:16.402]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[21:08:16.402]    </control>
[21:08:16.402]    <block atomic="false" info="">
[21:08:16.403]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[21:08:16.406]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[21:08:16.407]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[21:08:16.407]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[21:08:16.408]      __ap = 0; //lets make sure we reset the access point selection
[21:08:16.408]        // -> [__ap <= 0x00000000]
[21:08:16.408]    </block>
[21:08:16.408]  </sequence>
[21:08:16.408]  
[21:08:16.429]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[21:08:16.429]  
[21:08:16.429]  <debugvars>
[21:08:16.429]    // Pre-defined
[21:08:16.429]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[21:08:16.429]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[21:08:16.429]    __dp=0x00000000
[21:08:16.430]    __ap=0x00000000
[21:08:16.430]    __traceout=0x00000000      (Trace Disabled)
[21:08:16.430]    __errorcontrol=0x00000000  (Skip Errors="False")
[21:08:16.430]    __FlashAddr=0x00000000
[21:08:16.430]    __FlashLen=0x00000000
[21:08:16.430]    __FlashArg=0x00000000
[21:08:16.430]    __FlashOp=0x00000000
[21:08:16.430]    __Result=0x00000000
[21:08:16.430]  </debugvars>
[21:08:16.430]  
[21:08:16.430]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[21:08:16.430]    <block atomic="false" info="">
[21:08:16.430]      __var deviceID = 0;
[21:08:16.430]        // -> [deviceID <= 0x00000000]
[21:08:16.430]      __var version = 0;
[21:08:16.430]        // -> [version <= 0x00000000]
[21:08:16.431]      __var partNum = 0;
[21:08:16.431]        // -> [partNum <= 0x00000000]
[21:08:16.431]      __var manuf = 0;
[21:08:16.431]        // -> [manuf <= 0x00000000]
[21:08:16.431]      __var isMSPM0G1X0X_G3X0X = 0;
[21:08:16.431]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[21:08:16.431]      __var isProduction = 0;
[21:08:16.431]        // -> [isProduction <= 0x00000000]
[21:08:16.431]      __var continueId = 0;
[21:08:16.431]        // -> [continueId <= 0x00000000]
[21:08:16.431]      deviceID =   Read32(0x41C40004);
[21:08:16.438]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[21:08:16.438]        // -> [deviceID <= 0x2BB8802F]
[21:08:16.438]      version = deviceID >> 28;
[21:08:16.438]        // -> [version <= 0x00000002]
[21:08:16.438]      partNum = (deviceID & 0x0FFFF000) >> 12;
[21:08:16.438]        // -> [partNum <= 0x0000BB88]
[21:08:16.438]      manuf = (deviceID & 0x00000FFE) >> 1;
[21:08:16.438]        // -> [manuf <= 0x00000017]
[21:08:16.439]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[21:08:16.439]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[21:08:16.439]      isProduction = (version > 0);
[21:08:16.439]        // -> [isProduction <= 0x00000001]
[21:08:16.439]    </block>
[21:08:16.439]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[21:08:16.439]      // if-block "!isMSPM0G1X0X_G3X0X"
[21:08:16.439]        // =>  FALSE
[21:08:16.439]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[21:08:16.439]    </control>
[21:08:16.439]    <control if="continueId == 4" while="" timeout="0" info="">
[21:08:16.439]      // if-block "continueId == 4"
[21:08:16.439]        // =>  FALSE
[21:08:16.439]      // skip if-block "continueId == 4"
[21:08:16.439]    </control>
[21:08:16.439]    <control if="!isProduction" while="" timeout="0" info="">
[21:08:16.440]      // if-block "!isProduction"
[21:08:16.440]        // =>  FALSE
[21:08:16.440]      // skip if-block "!isProduction"
[21:08:16.440]    </control>
[21:08:16.440]  </sequence>
[21:08:16.440]  
[22:20:13.293]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:20:13.293]  
[22:20:13.294]  <debugvars>
[22:20:13.294]    // Pre-defined
[22:20:13.294]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:20:13.294]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:20:13.294]    __dp=0x00000000
[22:20:13.294]    __ap=0x00000000
[22:20:13.294]    __traceout=0x00000000      (Trace Disabled)
[22:20:13.294]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:20:13.294]    __FlashAddr=0x00000000
[22:20:13.294]    __FlashLen=0x00000000
[22:20:13.295]    __FlashArg=0x00000000
[22:20:13.295]    __FlashOp=0x00000000
[22:20:13.295]    __Result=0x00000000
[22:20:13.295]  </debugvars>
[22:20:13.295]  
[22:20:13.295]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:20:13.295]    <block atomic="false" info="">
[22:20:13.295]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:20:13.295]        // -> [isSWJ <= 0x00000001]
[22:20:13.295]      __var hasDormant = __protocol & 0x00020000;
[22:20:13.295]        // -> [hasDormant <= 0x00000000]
[22:20:13.295]      __var protType   = __protocol & 0x0000FFFF;
[22:20:13.295]        // -> [protType <= 0x00000002]
[22:20:13.295]    </block>
[22:20:13.295]    <control if="protType == 1" while="" timeout="0" info="">
[22:20:13.296]      // if-block "protType == 1"
[22:20:13.296]        // =>  FALSE
[22:20:13.296]      // skip if-block "protType == 1"
[22:20:13.296]    </control>
[22:20:13.296]    <control if="protType == 2" while="" timeout="0" info="">
[22:20:13.296]      // if-block "protType == 2"
[22:20:13.296]        // =>  TRUE
[22:20:13.296]      <control if="isSWJ" while="" timeout="0" info="">
[22:20:13.296]        // if-block "isSWJ"
[22:20:13.296]          // =>  TRUE
[22:20:13.296]        <control if="hasDormant" while="" timeout="0" info="">
[22:20:13.296]          // if-block "hasDormant"
[22:20:13.296]            // =>  FALSE
[22:20:13.296]          // skip if-block "hasDormant"
[22:20:13.296]        </control>
[22:20:13.297]        <control if="!hasDormant" while="" timeout="0" info="">
[22:20:13.297]          // if-block "!hasDormant"
[22:20:13.297]            // =>  TRUE
[22:20:13.297]          <block atomic="false" info="">
[22:20:13.297]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:20:13.299]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:20:13.299]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:20:13.301]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:20:13.301]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:20:13.303]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:20:13.303]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:20:13.305]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:20:13.305]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:20:13.307]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:20:13.307]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:20:13.308]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:20:13.309]          </block>
[22:20:13.309]          // end if-block "!hasDormant"
[22:20:13.309]        </control>
[22:20:13.309]        // end if-block "isSWJ"
[22:20:13.309]      </control>
[22:20:13.309]      <control if="!isSWJ" while="" timeout="0" info="">
[22:20:13.309]        // if-block "!isSWJ"
[22:20:13.309]          // =>  FALSE
[22:20:13.310]        // skip if-block "!isSWJ"
[22:20:13.310]      </control>
[22:20:13.310]      <block atomic="false" info="">
[22:20:13.310]        ReadDP(0x0);
[22:20:13.311]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:20:13.313]      </block>
[22:20:13.313]      // end if-block "protType == 2"
[22:20:13.313]    </control>
[22:20:13.313]  </sequence>
[22:20:13.313]  
[22:20:13.317]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:20:13.317]  
[22:20:13.317]  <debugvars>
[22:20:13.318]    // Pre-defined
[22:20:13.318]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:20:13.318]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:20:13.318]    __dp=0x00000000
[22:20:13.318]    __ap=0x00000000
[22:20:13.318]    __traceout=0x00000000      (Trace Disabled)
[22:20:13.318]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:20:13.319]    __FlashAddr=0x00000000
[22:20:13.319]    __FlashLen=0x00000000
[22:20:13.319]    __FlashArg=0x00000000
[22:20:13.319]    __FlashOp=0x00000000
[22:20:13.319]    __Result=0x00000000
[22:20:13.319]  </debugvars>
[22:20:13.319]  
[22:20:13.319]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:20:13.319]    <block atomic="false" info="">
[22:20:13.319]      __var SW_DP_ABORT       = 0x0;
[22:20:13.319]        // -> [SW_DP_ABORT <= 0x00000000]
[22:20:13.319]      __var DP_CTRL_STAT      = 0x4;
[22:20:13.319]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:20:13.319]      __var DP_SELECT         = 0x8;
[22:20:13.319]        // -> [DP_SELECT <= 0x00000008]
[22:20:13.320]      __var powered_down      = 0;
[22:20:13.320]        // -> [powered_down <= 0x00000000]
[22:20:13.320]      WriteDP(DP_SELECT, 0x00000000);
[22:20:13.322]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:20:13.322]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:20:13.324]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:20:13.324]        // -> [powered_down <= 0x00000001]
[22:20:13.324]    </block>
[22:20:13.324]    <control if="powered_down" while="" timeout="0" info="">
[22:20:13.324]      // if-block "powered_down"
[22:20:13.324]        // =>  TRUE
[22:20:13.324]      <block atomic="false" info="">
[22:20:13.324]        Message(0, "Debug/System power-up request sent");
[22:20:13.326]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:20:13.327]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:20:13.328]      </block>
[22:20:13.328]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:20:13.328]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.331]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.331]        // while-condition  =>  TRUE
[22:20:13.331]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.333]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.333]        // while-condition  =>  TRUE
[22:20:13.334]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.335]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.335]        // while-condition  =>  TRUE
[22:20:13.336]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.337]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.337]        // while-condition  =>  TRUE
[22:20:13.337]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.339]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.339]        // while-condition  =>  TRUE
[22:20:13.339]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.341]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.341]        // while-condition  =>  TRUE
[22:20:13.341]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.343]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.343]        // while-condition  =>  TRUE
[22:20:13.343]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.345]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.345]        // while-condition  =>  TRUE
[22:20:13.345]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.347]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.347]        // while-condition  =>  TRUE
[22:20:13.347]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.349]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.349]        // while-condition  =>  TRUE
[22:20:13.349]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.351]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.351]        // while-condition  =>  TRUE
[22:20:13.351]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.352]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.353]        // while-condition  =>  TRUE
[22:20:13.353]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.354]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.355]        // while-condition  =>  TRUE
[22:20:13.355]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.357]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.357]        // while-condition  =>  TRUE
[22:20:13.357]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.359]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.359]        // while-condition  =>  TRUE
[22:20:13.359]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.360]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.361]        // while-condition  =>  TRUE
[22:20:13.361]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.362]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.362]        // while-condition  =>  TRUE
[22:20:13.363]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.364]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.364]        // while-condition  =>  TRUE
[22:20:13.365]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.366]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.366]        // while-condition  =>  TRUE
[22:20:13.367]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.368]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.369]        // while-condition  =>  TRUE
[22:20:13.369]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.371]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.371]        // while-condition  =>  TRUE
[22:20:13.371]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.373]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.373]        // while-condition  =>  TRUE
[22:20:13.373]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.375]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.375]        // while-condition  =>  TRUE
[22:20:13.375]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.376]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.377]        // while-condition  =>  TRUE
[22:20:13.377]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.378]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.379]        // while-condition  =>  TRUE
[22:20:13.379]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.380]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.381]        // while-condition  =>  TRUE
[22:20:13.381]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.382]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.383]        // while-condition  =>  TRUE
[22:20:13.383]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.384]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.385]        // while-condition  =>  TRUE
[22:20:13.385]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.386]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.386]        // while-condition  =>  TRUE
[22:20:13.387]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.388]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.389]        // while-condition  =>  TRUE
[22:20:13.389]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.390]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.391]        // while-condition  =>  TRUE
[22:20:13.391]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.392]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.392]        // while-condition  =>  TRUE
[22:20:13.392]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.395]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.395]        // while-condition  =>  TRUE
[22:20:13.395]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.397]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.397]        // while-condition  =>  TRUE
[22:20:13.397]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.399]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.399]        // while-condition  =>  TRUE
[22:20:13.399]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.401]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.401]        // while-condition  =>  TRUE
[22:20:13.401]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.403]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.403]        // while-condition  =>  TRUE
[22:20:13.403]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.405]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.405]        // while-condition  =>  TRUE
[22:20:13.405]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.407]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.407]        // while-condition  =>  TRUE
[22:20:13.407]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.409]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.409]        // while-condition  =>  TRUE
[22:20:13.409]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.410]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.411]        // while-condition  =>  TRUE
[22:20:13.411]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.413]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.413]        // while-condition  =>  TRUE
[22:20:13.413]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.415]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.415]        // while-condition  =>  TRUE
[22:20:13.415]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.417]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.417]        // while-condition  =>  TRUE
[22:20:13.417]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.419]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.419]        // while-condition  =>  TRUE
[22:20:13.419]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.420]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.421]        // while-condition  =>  TRUE
[22:20:13.421]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.422]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.423]        // while-condition  =>  TRUE
[22:20:13.423]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.424]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.426]        // while-condition  =>  TRUE
[22:20:13.426]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.427]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.427]        // while-condition  =>  TRUE
[22:20:13.427]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.429]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.429]        // while-condition  =>  TRUE
[22:20:13.429]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.431]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.431]        // while-condition  =>  TRUE
[22:20:13.432]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.433]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.433]        // while-condition  =>  TRUE
[22:20:13.433]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.435]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.435]        // while-condition  =>  TRUE
[22:20:13.435]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.437]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.437]        // while-condition  =>  TRUE
[22:20:13.437]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.440]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.440]        // while-condition  =>  TRUE
[22:20:13.440]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.441]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.442]        // while-condition  =>  TRUE
[22:20:13.442]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.443]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.444]        // while-condition  =>  TRUE
[22:20:13.444]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.445]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.446]        // while-condition  =>  TRUE
[22:20:13.446]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.447]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.447]        // while-condition  =>  TRUE
[22:20:13.448]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.449]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.449]        // while-condition  =>  TRUE
[22:20:13.450]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.451]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.451]        // while-condition  =>  TRUE
[22:20:13.451]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.454]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.454]        // while-condition  =>  TRUE
[22:20:13.454]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.456]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.456]        // while-condition  =>  TRUE
[22:20:13.456]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.458]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.458]        // while-condition  =>  TRUE
[22:20:13.458]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.460]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.460]        // while-condition  =>  TRUE
[22:20:13.460]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.462]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.462]        // while-condition  =>  TRUE
[22:20:13.462]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.463]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.463]        // while-condition  =>  TRUE
[22:20:13.463]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.466]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.466]        // while-condition  =>  TRUE
[22:20:13.466]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.468]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.468]        // while-condition  =>  TRUE
[22:20:13.468]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.470]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.471]        // while-condition  =>  TRUE
[22:20:13.471]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.472]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.472]        // while-condition  =>  TRUE
[22:20:13.473]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.474]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.474]        // while-condition  =>  TRUE
[22:20:13.474]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.476]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.476]        // while-condition  =>  TRUE
[22:20:13.476]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.477]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.477]        // while-condition  =>  TRUE
[22:20:13.479]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.480]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.480]        // while-condition  =>  TRUE
[22:20:13.480]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.482]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.482]        // while-condition  =>  TRUE
[22:20:13.482]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.483]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.484]        // while-condition  =>  TRUE
[22:20:13.484]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.485]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.486]        // while-condition  =>  TRUE
[22:20:13.486]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.487]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.487]        // while-condition  =>  TRUE
[22:20:13.489]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.490]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.490]        // while-condition  =>  TRUE
[22:20:13.491]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.492]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.492]        // while-condition  =>  TRUE
[22:20:13.492]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.494]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.494]        // while-condition  =>  TRUE
[22:20:13.494]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.496]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.496]        // while-condition  =>  TRUE
[22:20:13.496]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.498]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.498]        // while-condition  =>  TRUE
[22:20:13.498]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.500]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.500]        // while-condition  =>  TRUE
[22:20:13.500]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.501]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.502]        // while-condition  =>  TRUE
[22:20:13.502]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.504]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.504]        // while-condition  =>  TRUE
[22:20:13.504]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.506]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.506]        // while-condition  =>  TRUE
[22:20:13.506]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.508]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.508]        // while-condition  =>  TRUE
[22:20:13.508]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.510]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.510]        // while-condition  =>  TRUE
[22:20:13.510]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.511]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.511]        // while-condition  =>  TRUE
[22:20:13.511]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.514]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.514]        // while-condition  =>  TRUE
[22:20:13.514]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.516]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.516]        // while-condition  =>  TRUE
[22:20:13.516]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.517]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.518]        // while-condition  =>  TRUE
[22:20:13.518]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.519]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.519]        // while-condition  =>  TRUE
[22:20:13.519]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.522]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.522]        // while-condition  =>  TRUE
[22:20:13.523]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.524]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.524]        // while-condition  =>  TRUE
[22:20:13.524]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.526]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.526]        // while-condition  =>  TRUE
[22:20:13.526]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.528]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.528]        // while-condition  =>  TRUE
[22:20:13.528]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.530]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.530]        // while-condition  =>  TRUE
[22:20:13.530]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.532]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.532]        // while-condition  =>  TRUE
[22:20:13.532]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.534]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.534]        // while-condition  =>  TRUE
[22:20:13.534]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.536]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.536]        // while-condition  =>  TRUE
[22:20:13.536]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.538]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.538]        // while-condition  =>  TRUE
[22:20:13.538]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.539]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.540]        // while-condition  =>  TRUE
[22:20:13.540]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.542]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.542]        // while-condition  =>  TRUE
[22:20:13.543]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.544]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.544]        // while-condition  =>  TRUE
[22:20:13.544]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.546]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.546]        // while-condition  =>  TRUE
[22:20:13.546]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.548]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.548]        // while-condition  =>  TRUE
[22:20:13.548]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.550]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.550]        // while-condition  =>  TRUE
[22:20:13.550]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.551]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.552]        // while-condition  =>  TRUE
[22:20:13.552]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.553]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.553]        // while-condition  =>  TRUE
[22:20:13.554]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.556]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.556]        // while-condition  =>  TRUE
[22:20:13.557]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.558]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.558]        // while-condition  =>  TRUE
[22:20:13.559]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.560]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.560]        // while-condition  =>  TRUE
[22:20:13.560]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.561]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.561]        // while-condition  =>  TRUE
[22:20:13.562]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.564]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.565]        // while-condition  =>  TRUE
[22:20:13.565]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.566]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.566]        // while-condition  =>  TRUE
[22:20:13.566]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.568]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.568]        // while-condition  =>  TRUE
[22:20:13.569]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.570]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.570]        // while-condition  =>  TRUE
[22:20:13.570]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.572]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.572]        // while-condition  =>  TRUE
[22:20:13.572]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.574]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.574]        // while-condition  =>  TRUE
[22:20:13.574]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.575]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.576]        // while-condition  =>  TRUE
[22:20:13.576]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.577]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.578]        // while-condition  =>  TRUE
[22:20:13.578]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.579]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.580]        // while-condition  =>  TRUE
[22:20:13.580]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.582]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.583]        // while-condition  =>  TRUE
[22:20:13.583]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.585]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.585]        // while-condition  =>  TRUE
[22:20:13.585]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.587]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.587]        // while-condition  =>  TRUE
[22:20:13.588]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.589]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.589]        // while-condition  =>  TRUE
[22:20:13.589]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.591]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.591]        // while-condition  =>  TRUE
[22:20:13.591]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.593]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.593]        // while-condition  =>  TRUE
[22:20:13.593]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.595]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.595]        // while-condition  =>  TRUE
[22:20:13.595]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.597]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.597]        // while-condition  =>  TRUE
[22:20:13.597]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.599]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.599]        // while-condition  =>  TRUE
[22:20:13.599]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.601]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.601]        // while-condition  =>  TRUE
[22:20:13.601]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.603]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.603]        // while-condition  =>  TRUE
[22:20:13.603]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.605]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.605]        // while-condition  =>  TRUE
[22:20:13.605]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.606]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.606]        // while-condition  =>  TRUE
[22:20:13.607]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.608]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.609]        // while-condition  =>  TRUE
[22:20:13.609]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.610]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.611]        // while-condition  =>  TRUE
[22:20:13.611]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.613]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.613]        // while-condition  =>  TRUE
[22:20:13.613]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.615]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.615]        // while-condition  =>  TRUE
[22:20:13.615]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.616]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.616]        // while-condition  =>  TRUE
[22:20:13.616]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.619]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.619]        // while-condition  =>  TRUE
[22:20:13.619]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.621]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.621]        // while-condition  =>  TRUE
[22:20:13.621]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.623]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.623]        // while-condition  =>  TRUE
[22:20:13.623]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.626]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.626]        // while-condition  =>  TRUE
[22:20:13.626]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.628]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.628]        // while-condition  =>  TRUE
[22:20:13.628]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.630]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.630]        // while-condition  =>  TRUE
[22:20:13.630]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.632]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.632]        // while-condition  =>  TRUE
[22:20:13.632]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.634]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.634]        // while-condition  =>  TRUE
[22:20:13.634]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.636]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.636]        // while-condition  =>  TRUE
[22:20:13.636]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.637]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.637]        // while-condition  =>  TRUE
[22:20:13.639]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.641]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.641]        // while-condition  =>  TRUE
[22:20:13.641]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.643]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.643]        // while-condition  =>  TRUE
[22:20:13.643]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.645]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.645]        // while-condition  =>  TRUE
[22:20:13.645]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.647]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.647]        // while-condition  =>  TRUE
[22:20:13.647]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.649]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.649]        // while-condition  =>  TRUE
[22:20:13.649]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.651]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.651]        // while-condition  =>  TRUE
[22:20:13.651]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.653]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.653]        // while-condition  =>  TRUE
[22:20:13.653]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.656]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.656]        // while-condition  =>  TRUE
[22:20:13.657]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.659]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.659]        // while-condition  =>  TRUE
[22:20:13.659]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.661]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.662]        // while-condition  =>  TRUE
[22:20:13.662]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.663]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.664]        // while-condition  =>  TRUE
[22:20:13.664]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.666]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.666]        // while-condition  =>  TRUE
[22:20:13.666]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.667]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.668]        // while-condition  =>  TRUE
[22:20:13.668]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.670]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.671]        // while-condition  =>  TRUE
[22:20:13.671]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.672]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.673]        // while-condition  =>  TRUE
[22:20:13.673]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.675]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.675]        // while-condition  =>  TRUE
[22:20:13.675]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.676]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.677]        // while-condition  =>  TRUE
[22:20:13.677]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.679]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.679]        // while-condition  =>  TRUE
[22:20:13.679]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.681]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.681]        // while-condition  =>  TRUE
[22:20:13.681]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.683]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.683]        // while-condition  =>  TRUE
[22:20:13.683]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.685]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.685]        // while-condition  =>  TRUE
[22:20:13.687]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.688]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.689]        // while-condition  =>  TRUE
[22:20:13.689]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.691]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.691]        // while-condition  =>  TRUE
[22:20:13.691]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.693]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.693]        // while-condition  =>  TRUE
[22:20:13.693]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.695]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.695]        // while-condition  =>  TRUE
[22:20:13.695]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.697]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.697]        // while-condition  =>  TRUE
[22:20:13.697]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.699]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.699]        // while-condition  =>  TRUE
[22:20:13.699]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.701]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.701]        // while-condition  =>  TRUE
[22:20:13.701]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.703]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.703]        // while-condition  =>  TRUE
[22:20:13.703]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.705]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.705]        // while-condition  =>  TRUE
[22:20:13.705]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.707]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.707]        // while-condition  =>  TRUE
[22:20:13.707]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.709]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.709]        // while-condition  =>  TRUE
[22:20:13.709]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.711]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.711]        // while-condition  =>  TRUE
[22:20:13.711]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.713]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.713]        // while-condition  =>  TRUE
[22:20:13.713]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.715]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.715]        // while-condition  =>  TRUE
[22:20:13.716]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.718]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.718]        // while-condition  =>  TRUE
[22:20:13.718]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.720]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.721]        // while-condition  =>  TRUE
[22:20:13.721]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.722]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.722]        // while-condition  =>  TRUE
[22:20:13.723]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.724]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.724]        // while-condition  =>  TRUE
[22:20:13.724]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.726]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.726]        // while-condition  =>  TRUE
[22:20:13.726]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.728]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.728]        // while-condition  =>  TRUE
[22:20:13.728]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.730]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.730]        // while-condition  =>  TRUE
[22:20:13.730]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.732]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.732]        // while-condition  =>  TRUE
[22:20:13.732]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.734]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.734]        // while-condition  =>  TRUE
[22:20:13.734]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.736]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.736]        // while-condition  =>  TRUE
[22:20:13.736]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.738]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.738]        // while-condition  =>  TRUE
[22:20:13.738]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.740]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.740]        // while-condition  =>  TRUE
[22:20:13.740]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.742]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.742]        // while-condition  =>  TRUE
[22:20:13.742]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.743]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.744]        // while-condition  =>  TRUE
[22:20:13.744]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.745]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.745]        // while-condition  =>  TRUE
[22:20:13.745]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.748]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.748]        // while-condition  =>  TRUE
[22:20:13.748]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.750]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.750]        // while-condition  =>  TRUE
[22:20:13.750]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.752]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.752]        // while-condition  =>  TRUE
[22:20:13.752]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.753]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.754]        // while-condition  =>  TRUE
[22:20:13.754]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.755]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.756]        // while-condition  =>  TRUE
[22:20:13.756]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.757]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.758]        // while-condition  =>  TRUE
[22:20:13.758]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.759]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.760]        // while-condition  =>  TRUE
[22:20:13.760]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.761]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.761]        // while-condition  =>  TRUE
[22:20:13.761]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.763]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.764]        // while-condition  =>  TRUE
[22:20:13.764]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.765]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.766]        // while-condition  =>  TRUE
[22:20:13.766]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.768]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.768]        // while-condition  =>  TRUE
[22:20:13.768]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.769]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.770]        // while-condition  =>  TRUE
[22:20:13.770]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.771]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.772]        // while-condition  =>  TRUE
[22:20:13.772]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.773]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.774]        // while-condition  =>  TRUE
[22:20:13.774]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.775]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.776]        // while-condition  =>  TRUE
[22:20:13.776]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.777]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.778]        // while-condition  =>  TRUE
[22:20:13.778]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.780]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.780]        // while-condition  =>  TRUE
[22:20:13.780]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.782]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.782]        // while-condition  =>  TRUE
[22:20:13.782]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.784]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.784]        // while-condition  =>  TRUE
[22:20:13.784]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.786]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.786]        // while-condition  =>  TRUE
[22:20:13.786]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.788]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.788]        // while-condition  =>  TRUE
[22:20:13.788]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.790]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.790]        // while-condition  =>  TRUE
[22:20:13.790]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.792]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.792]        // while-condition  =>  TRUE
[22:20:13.792]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.794]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.794]        // while-condition  =>  TRUE
[22:20:13.794]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.796]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.796]        // while-condition  =>  TRUE
[22:20:13.796]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.798]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.798]        // while-condition  =>  TRUE
[22:20:13.798]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.800]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.800]        // while-condition  =>  TRUE
[22:20:13.800]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.802]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.802]        // while-condition  =>  TRUE
[22:20:13.802]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.804]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.804]        // while-condition  =>  TRUE
[22:20:13.804]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.806]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.806]        // while-condition  =>  TRUE
[22:20:13.806]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.807]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.808]        // while-condition  =>  TRUE
[22:20:13.808]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.809]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.809]        // while-condition  =>  TRUE
[22:20:13.809]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.812]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.812]        // while-condition  =>  TRUE
[22:20:13.812]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.814]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.814]        // while-condition  =>  TRUE
[22:20:13.814]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.816]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.816]        // while-condition  =>  TRUE
[22:20:13.816]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.818]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.818]        // while-condition  =>  TRUE
[22:20:13.818]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.820]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.820]        // while-condition  =>  TRUE
[22:20:13.820]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.821]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.822]        // while-condition  =>  TRUE
[22:20:13.822]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.823]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.824]        // while-condition  =>  TRUE
[22:20:13.824]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.826]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.827]        // while-condition  =>  TRUE
[22:20:13.827]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.828]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.829]        // while-condition  =>  TRUE
[22:20:13.829]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.830]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.830]        // while-condition  =>  TRUE
[22:20:13.830]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.832]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.832]        // while-condition  =>  TRUE
[22:20:13.832]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.834]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.834]        // while-condition  =>  TRUE
[22:20:13.834]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.836]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.836]        // while-condition  =>  TRUE
[22:20:13.836]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.838]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.838]        // while-condition  =>  TRUE
[22:20:13.838]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.839]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.840]        // while-condition  =>  TRUE
[22:20:13.840]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.843]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.843]        // while-condition  =>  TRUE
[22:20:13.843]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.845]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.845]        // while-condition  =>  TRUE
[22:20:13.845]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.847]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.847]        // while-condition  =>  TRUE
[22:20:13.847]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.849]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.849]        // while-condition  =>  TRUE
[22:20:13.849]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.851]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.851]        // while-condition  =>  TRUE
[22:20:13.851]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.853]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.853]        // while-condition  =>  TRUE
[22:20:13.853]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.854]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.854]        // while-condition  =>  TRUE
[22:20:13.855]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.856]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.857]        // while-condition  =>  TRUE
[22:20:13.857]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.858]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.858]        // while-condition  =>  TRUE
[22:20:13.859]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.861]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.861]        // while-condition  =>  TRUE
[22:20:13.862]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.863]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.863]        // while-condition  =>  TRUE
[22:20:13.863]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.865]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.865]        // while-condition  =>  TRUE
[22:20:13.866]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.867]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.867]        // while-condition  =>  TRUE
[22:20:13.867]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.869]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.869]        // while-condition  =>  TRUE
[22:20:13.869]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.871]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.871]        // while-condition  =>  TRUE
[22:20:13.871]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.872]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.873]        // while-condition  =>  TRUE
[22:20:13.873]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.874]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.875]        // while-condition  =>  TRUE
[22:20:13.875]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.877]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.877]        // while-condition  =>  TRUE
[22:20:13.877]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.879]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.879]        // while-condition  =>  TRUE
[22:20:13.879]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.881]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.881]        // while-condition  =>  TRUE
[22:20:13.881]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.883]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.883]        // while-condition  =>  TRUE
[22:20:13.883]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.884]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.885]        // while-condition  =>  TRUE
[22:20:13.885]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.888]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.888]        // while-condition  =>  TRUE
[22:20:13.888]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.890]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.890]        // while-condition  =>  TRUE
[22:20:13.890]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.892]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.892]        // while-condition  =>  TRUE
[22:20:13.892]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.894]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.894]        // while-condition  =>  TRUE
[22:20:13.894]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.895]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.895]        // while-condition  =>  TRUE
[22:20:13.897]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.898]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.898]        // while-condition  =>  TRUE
[22:20:13.898]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.900]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.900]        // while-condition  =>  TRUE
[22:20:13.900]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.902]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.902]        // while-condition  =>  TRUE
[22:20:13.902]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.904]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.904]        // while-condition  =>  TRUE
[22:20:13.904]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.906]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.906]        // while-condition  =>  TRUE
[22:20:13.906]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.908]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.908]        // while-condition  =>  TRUE
[22:20:13.908]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.910]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.910]        // while-condition  =>  TRUE
[22:20:13.910]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.912]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.912]        // while-condition  =>  TRUE
[22:20:13.912]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.914]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.914]        // while-condition  =>  TRUE
[22:20:13.914]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.916]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.916]        // while-condition  =>  TRUE
[22:20:13.916]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.918]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.918]        // while-condition  =>  TRUE
[22:20:13.918]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.920]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.920]        // while-condition  =>  TRUE
[22:20:13.920]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.922]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.922]        // while-condition  =>  TRUE
[22:20:13.922]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.924]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.924]        // while-condition  =>  TRUE
[22:20:13.924]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.926]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.926]        // while-condition  =>  TRUE
[22:20:13.926]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.928]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.928]        // while-condition  =>  TRUE
[22:20:13.928]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.930]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.930]        // while-condition  =>  TRUE
[22:20:13.930]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.931]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.931]        // while-condition  =>  TRUE
[22:20:13.933]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.934]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.934]        // while-condition  =>  TRUE
[22:20:13.934]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.936]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.936]        // while-condition  =>  TRUE
[22:20:13.936]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.938]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.938]        // while-condition  =>  TRUE
[22:20:13.938]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.940]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.940]        // while-condition  =>  TRUE
[22:20:13.940]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.942]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.942]        // while-condition  =>  TRUE
[22:20:13.942]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.944]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.944]        // while-condition  =>  TRUE
[22:20:13.944]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.946]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.946]        // while-condition  =>  TRUE
[22:20:13.946]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.947]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.949]        // while-condition  =>  TRUE
[22:20:13.949]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.951]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.952]        // while-condition  =>  TRUE
[22:20:13.952]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.953]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.953]        // while-condition  =>  TRUE
[22:20:13.954]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.955]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.955]        // while-condition  =>  TRUE
[22:20:13.955]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.957]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.957]        // while-condition  =>  TRUE
[22:20:13.958]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.959]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.959]        // while-condition  =>  TRUE
[22:20:13.959]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.960]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.961]        // while-condition  =>  TRUE
[22:20:13.961]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.962]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.964]        // while-condition  =>  TRUE
[22:20:13.964]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.966]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.966]        // while-condition  =>  TRUE
[22:20:13.966]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.968]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.968]        // while-condition  =>  TRUE
[22:20:13.968]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.970]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.970]        // while-condition  =>  TRUE
[22:20:13.970]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.972]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.972]        // while-condition  =>  TRUE
[22:20:13.972]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.974]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.974]        // while-condition  =>  TRUE
[22:20:13.974]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.976]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.976]        // while-condition  =>  TRUE
[22:20:13.976]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.978]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.978]        // while-condition  =>  TRUE
[22:20:13.978]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.979]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.980]        // while-condition  =>  TRUE
[22:20:13.980]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.982]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.982]        // while-condition  =>  TRUE
[22:20:13.983]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.984]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.984]        // while-condition  =>  TRUE
[22:20:13.984]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.986]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.986]        // while-condition  =>  TRUE
[22:20:13.986]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.988]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.988]        // while-condition  =>  TRUE
[22:20:13.988]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.990]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.990]        // while-condition  =>  TRUE
[22:20:13.990]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.992]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.992]        // while-condition  =>  TRUE
[22:20:13.992]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.994]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.994]        // while-condition  =>  TRUE
[22:20:13.994]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.996]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.996]        // while-condition  =>  TRUE
[22:20:13.996]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:13.998]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:13.998]        // while-condition  =>  TRUE
[22:20:13.998]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.000]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.000]        // while-condition  =>  TRUE
[22:20:14.000]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.002]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.002]        // while-condition  =>  TRUE
[22:20:14.002]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.004]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.004]        // while-condition  =>  TRUE
[22:20:14.004]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.006]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.006]        // while-condition  =>  TRUE
[22:20:14.006]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.008]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.008]        // while-condition  =>  TRUE
[22:20:14.008]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.010]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.010]        // while-condition  =>  TRUE
[22:20:14.010]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.013]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.013]        // while-condition  =>  TRUE
[22:20:14.013]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.015]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.015]        // while-condition  =>  TRUE
[22:20:14.016]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.017]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.017]        // while-condition  =>  TRUE
[22:20:14.017]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.019]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.019]        // while-condition  =>  TRUE
[22:20:14.019]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.021]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.021]        // while-condition  =>  TRUE
[22:20:14.021]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.022]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.022]        // while-condition  =>  TRUE
[22:20:14.023]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.024]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.025]        // while-condition  =>  TRUE
[22:20:14.025]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.026]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.026]        // while-condition  =>  TRUE
[22:20:14.027]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.028]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.028]        // while-condition  =>  TRUE
[22:20:14.029]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.031]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.031]        // while-condition  =>  TRUE
[22:20:14.031]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.033]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.033]        // while-condition  =>  TRUE
[22:20:14.033]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.035]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.035]        // while-condition  =>  TRUE
[22:20:14.035]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.037]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.037]        // while-condition  =>  TRUE
[22:20:14.037]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.039]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.039]        // while-condition  =>  TRUE
[22:20:14.039]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.041]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.041]        // while-condition  =>  TRUE
[22:20:14.041]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.043]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.043]        // while-condition  =>  TRUE
[22:20:14.043]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.045]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.046]        // while-condition  =>  TRUE
[22:20:14.046]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.048]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.048]        // while-condition  =>  TRUE
[22:20:14.049]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.050]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.050]        // while-condition  =>  TRUE
[22:20:14.050]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.052]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.052]        // while-condition  =>  TRUE
[22:20:14.052]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.054]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.054]        // while-condition  =>  TRUE
[22:20:14.054]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.056]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.056]        // while-condition  =>  TRUE
[22:20:14.056]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.058]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.058]        // while-condition  =>  TRUE
[22:20:14.058]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.062]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.062]        // while-condition  =>  TRUE
[22:20:14.062]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.063]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.063]        // while-condition  =>  TRUE
[22:20:14.064]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.065]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.065]        // while-condition  =>  TRUE
[22:20:14.065]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.067]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.067]        // while-condition  =>  TRUE
[22:20:14.067]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.069]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.069]        // while-condition  =>  TRUE
[22:20:14.069]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.071]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.071]        // while-condition  =>  TRUE
[22:20:14.072]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.074]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.074]        // while-condition  =>  TRUE
[22:20:14.074]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.076]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.076]        // while-condition  =>  TRUE
[22:20:14.076]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.078]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.078]        // while-condition  =>  TRUE
[22:20:14.078]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.081]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.081]        // while-condition  =>  TRUE
[22:20:14.081]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.083]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.083]        // while-condition  =>  TRUE
[22:20:14.083]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.086]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.086]        // while-condition  =>  TRUE
[22:20:14.086]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.087]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.087]        // while-condition  =>  TRUE
[22:20:14.087]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.089]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.089]        // while-condition  =>  TRUE
[22:20:14.089]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.091]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.091]        // while-condition  =>  TRUE
[22:20:14.091]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.093]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.093]        // while-condition  =>  TRUE
[22:20:14.093]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.095]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.095]        // while-condition  =>  TRUE
[22:20:14.096]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.097]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.097]        // while-condition  =>  TRUE
[22:20:14.098]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.099]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.100]        // while-condition  =>  TRUE
[22:20:14.100]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.101]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.101]        // while-condition  =>  TRUE
[22:20:14.102]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.103]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.103]        // while-condition  =>  TRUE
[22:20:14.104]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.105]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.105]        // while-condition  =>  TRUE
[22:20:14.106]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.107]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.107]        // while-condition  =>  TRUE
[22:20:14.107]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.109]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.109]        // while-condition  =>  TRUE
[22:20:14.109]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.111]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.111]        // while-condition  =>  TRUE
[22:20:14.111]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.113]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.113]        // while-condition  =>  TRUE
[22:20:14.113]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.115]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.115]        // while-condition  =>  TRUE
[22:20:14.116]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.117]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.117]        // while-condition  =>  TRUE
[22:20:14.117]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.119]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.119]        // while-condition  =>  TRUE
[22:20:14.119]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.121]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.121]        // while-condition  =>  TRUE
[22:20:14.121]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.123]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.123]        // while-condition  =>  TRUE
[22:20:14.123]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.125]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.125]        // while-condition  =>  TRUE
[22:20:14.125]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.127]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.127]        // while-condition  =>  TRUE
[22:20:14.127]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.129]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.129]        // while-condition  =>  TRUE
[22:20:14.129]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.130]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.130]        // while-condition  =>  TRUE
[22:20:14.130]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.133]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.133]        // while-condition  =>  TRUE
[22:20:14.133]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.136]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.136]        // while-condition  =>  TRUE
[22:20:14.136]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.138]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.138]        // while-condition  =>  TRUE
[22:20:14.138]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.139]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.139]        // while-condition  =>  TRUE
[22:20:14.141]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.142]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.143]        // while-condition  =>  TRUE
[22:20:14.143]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.144]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.145]        // while-condition  =>  TRUE
[22:20:14.145]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.146]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.147]        // while-condition  =>  TRUE
[22:20:14.147]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.148]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.148]        // while-condition  =>  TRUE
[22:20:14.148]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.150]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.150]        // while-condition  =>  TRUE
[22:20:14.150]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.151]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.152]        // while-condition  =>  TRUE
[22:20:14.152]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.153]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.154]        // while-condition  =>  TRUE
[22:20:14.154]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.156]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.156]        // while-condition  =>  TRUE
[22:20:14.156]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.158]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.159]        // while-condition  =>  TRUE
[22:20:14.159]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.160]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.161]        // while-condition  =>  TRUE
[22:20:14.161]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.163]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.163]        // while-condition  =>  TRUE
[22:20:14.163]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.164]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.165]        // while-condition  =>  TRUE
[22:20:14.165]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.167]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.168]        // while-condition  =>  TRUE
[22:20:14.168]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.169]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.169]        // while-condition  =>  TRUE
[22:20:14.170]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.171]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.171]        // while-condition  =>  TRUE
[22:20:14.171]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.173]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.173]        // while-condition  =>  TRUE
[22:20:14.173]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.175]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.175]        // while-condition  =>  TRUE
[22:20:14.175]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.177]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.177]        // while-condition  =>  TRUE
[22:20:14.177]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.179]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.179]        // while-condition  =>  TRUE
[22:20:14.179]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.181]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.181]        // while-condition  =>  TRUE
[22:20:14.181]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.183]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.183]        // while-condition  =>  TRUE
[22:20:14.183]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.184]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.184]        // while-condition  =>  TRUE
[22:20:14.185]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.187]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.187]        // while-condition  =>  TRUE
[22:20:14.187]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.189]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.189]        // while-condition  =>  TRUE
[22:20:14.189]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.191]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.191]        // while-condition  =>  TRUE
[22:20:14.191]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.193]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.193]        // while-condition  =>  TRUE
[22:20:14.193]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.195]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.195]        // while-condition  =>  TRUE
[22:20:14.195]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.197]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.197]        // while-condition  =>  TRUE
[22:20:14.197]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.198]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.199]        // while-condition  =>  TRUE
[22:20:14.199]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.201]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.201]        // while-condition  =>  TRUE
[22:20:14.201]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.203]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.203]        // while-condition  =>  TRUE
[22:20:14.203]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.206]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.206]        // while-condition  =>  TRUE
[22:20:14.206]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.208]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.208]        // while-condition  =>  TRUE
[22:20:14.208]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.210]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.210]        // while-condition  =>  TRUE
[22:20:14.210]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.212]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.212]        // while-condition  =>  TRUE
[22:20:14.212]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.214]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.214]        // while-condition  =>  TRUE
[22:20:14.214]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.216]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.216]        // while-condition  =>  TRUE
[22:20:14.216]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.217]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.217]        // while-condition  =>  TRUE
[22:20:14.217]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.220]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.220]        // while-condition  =>  TRUE
[22:20:14.220]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.222]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.222]        // while-condition  =>  TRUE
[22:20:14.222]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.224]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.224]        // while-condition  =>  TRUE
[22:20:14.224]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.226]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.226]        // while-condition  =>  TRUE
[22:20:14.226]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.228]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.229]        // while-condition  =>  TRUE
[22:20:14.229]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.230]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.230]        // while-condition  =>  TRUE
[22:20:14.230]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.232]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.232]        // while-condition  =>  TRUE
[22:20:14.232]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.234]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.234]        // while-condition  =>  TRUE
[22:20:14.234]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.236]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.236]        // while-condition  =>  TRUE
[22:20:14.236]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.238]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.238]        // while-condition  =>  TRUE
[22:20:14.238]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.240]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.240]        // while-condition  =>  TRUE
[22:20:14.240]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.243]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.243]        // while-condition  =>  TRUE
[22:20:14.243]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.245]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.246]        // while-condition  =>  TRUE
[22:20:14.246]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.248]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.248]        // while-condition  =>  TRUE
[22:20:14.248]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.250]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.250]        // while-condition  =>  TRUE
[22:20:14.250]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.252]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.252]        // while-condition  =>  TRUE
[22:20:14.252]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.254]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.254]        // while-condition  =>  TRUE
[22:20:14.254]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.256]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.256]        // while-condition  =>  TRUE
[22:20:14.256]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.258]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.258]        // while-condition  =>  TRUE
[22:20:14.258]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.259]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.260]        // while-condition  =>  TRUE
[22:20:14.260]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.261]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.262]        // while-condition  =>  TRUE
[22:20:14.262]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.263]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.264]        // while-condition  =>  TRUE
[22:20:14.264]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.265]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.265]        // while-condition  =>  TRUE
[22:20:14.267]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.268]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.268]        // while-condition  =>  TRUE
[22:20:14.268]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.270]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.270]        // while-condition  =>  TRUE
[22:20:14.270]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.272]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.272]        // while-condition  =>  TRUE
[22:20:14.272]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.274]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.274]        // while-condition  =>  TRUE
[22:20:14.274]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.276]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.276]        // while-condition  =>  TRUE
[22:20:14.276]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.278]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.278]        // while-condition  =>  TRUE
[22:20:14.278]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.280]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.280]        // while-condition  =>  TRUE
[22:20:14.280]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.282]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.282]        // while-condition  =>  TRUE
[22:20:14.282]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.284]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.284]        // while-condition  =>  TRUE
[22:20:14.284]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.286]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.286]        // while-condition  =>  TRUE
[22:20:14.286]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.288]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.288]        // while-condition  =>  TRUE
[22:20:14.288]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.290]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.291]        // while-condition  =>  TRUE
[22:20:14.291]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.293]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.293]        // while-condition  =>  TRUE
[22:20:14.294]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.295]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.295]        // while-condition  =>  TRUE
[22:20:14.295]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.297]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.297]        // while-condition  =>  TRUE
[22:20:14.297]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.299]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.299]        // while-condition  =>  TRUE
[22:20:14.299]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.301]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.303]        // while-condition  =>  TRUE
[22:20:14.303]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.304]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.304]        // while-condition  =>  TRUE
[22:20:14.304]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.306]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.306]        // while-condition  =>  TRUE
[22:20:14.306]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.308]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.308]        // while-condition  =>  TRUE
[22:20:14.308]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.310]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.310]        // while-condition  =>  TRUE
[22:20:14.310]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.312]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.312]        // while-condition  =>  TRUE
[22:20:14.312]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.314]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.315]        // while-condition  =>  TRUE
[22:20:14.315]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.317]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.317]        // while-condition  =>  TRUE
[22:20:14.317]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.320]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.320]        // while-condition  =>  TRUE
[22:20:14.320]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.323]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:20:14.323]        // while-condition  =>  TRUE
[22:20:14.323]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:20:14.323]        // while  =>  TIMEOUT
[22:20:14.323]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:20:14.323]      </control>
[22:20:14.323]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:20:14.323]        // if-block "(__protocol & 0xFFFF) == 1"
[22:20:14.323]          // =>  FALSE
[22:20:14.323]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:20:14.324]      </control>
[22:20:14.324]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:20:14.324]        // if-block "(__protocol & 0xFFFF) == 2"
[22:20:14.324]          // =>  TRUE
[22:20:14.324]        <block atomic="false" info="">
[22:20:14.324]          Message(0, "executing SWD power up");
[22:20:14.325]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:20:14.327]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:20:14.329]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:20:14.330]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:20:14.331]        </block>
[22:20:14.331]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:20:14.331]      </control>
[22:20:14.331]      // end if-block "powered_down"
[22:20:14.331]    </control>
[22:20:14.331]    <block atomic="false" info="">
[22:20:14.331]      __var DEBUG_PORT_VAL    = 0;
[22:20:14.331]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:20:14.331]      __var ACCESS_POINT_VAL  = 0;
[22:20:14.331]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:20:14.332]      __ap = 1; 
[22:20:14.332]        // -> [__ap <= 0x00000001]
[22:20:14.332]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:20:14.345]  
[22:20:14.345]  !!! E310 : Debug access failed - cannot write value 0x00000004 to AP register 0x0000000C (AP '0x00000001'))
[22:20:14.345]  
[22:20:14.349]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:20:14.349]    </block>
[22:20:14.350]  </sequence>
[22:20:14.350]  
[22:21:15.624]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:21:15.624]  
[22:21:15.625]  <debugvars>
[22:21:15.625]    // Pre-defined
[22:21:15.625]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:21:15.626]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:21:15.626]    __dp=0x00000000
[22:21:15.626]    __ap=0x00000000
[22:21:15.626]    __traceout=0x00000000      (Trace Disabled)
[22:21:15.627]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:21:15.627]    __FlashAddr=0x00000000
[22:21:15.627]    __FlashLen=0x00000000
[22:21:15.627]    __FlashArg=0x00000000
[22:21:15.627]    __FlashOp=0x00000000
[22:21:15.627]    __Result=0x00000000
[22:21:15.627]  </debugvars>
[22:21:15.627]  
[22:21:15.627]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:21:15.628]    <block atomic="false" info="">
[22:21:15.628]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:21:15.628]        // -> [isSWJ <= 0x00000001]
[22:21:15.628]      __var hasDormant = __protocol & 0x00020000;
[22:21:15.628]        // -> [hasDormant <= 0x00000000]
[22:21:15.628]      __var protType   = __protocol & 0x0000FFFF;
[22:21:15.628]        // -> [protType <= 0x00000002]
[22:21:15.628]    </block>
[22:21:15.628]    <control if="protType == 1" while="" timeout="0" info="">
[22:21:15.628]      // if-block "protType == 1"
[22:21:15.628]        // =>  FALSE
[22:21:15.628]      // skip if-block "protType == 1"
[22:21:15.628]    </control>
[22:21:15.628]    <control if="protType == 2" while="" timeout="0" info="">
[22:21:15.629]      // if-block "protType == 2"
[22:21:15.629]        // =>  TRUE
[22:21:15.629]      <control if="isSWJ" while="" timeout="0" info="">
[22:21:15.629]        // if-block "isSWJ"
[22:21:15.629]          // =>  TRUE
[22:21:15.629]        <control if="hasDormant" while="" timeout="0" info="">
[22:21:15.629]          // if-block "hasDormant"
[22:21:15.629]            // =>  FALSE
[22:21:15.629]          // skip if-block "hasDormant"
[22:21:15.629]        </control>
[22:21:15.630]        <control if="!hasDormant" while="" timeout="0" info="">
[22:21:15.630]          // if-block "!hasDormant"
[22:21:15.630]            // =>  TRUE
[22:21:15.630]          <block atomic="false" info="">
[22:21:15.630]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:21:15.632]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:21:15.632]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:21:15.634]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:21:15.634]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:21:15.636]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:21:15.637]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:21:15.638]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:21:15.639]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:21:15.640]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:21:15.640]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:21:15.642]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:21:15.642]          </block>
[22:21:15.642]          // end if-block "!hasDormant"
[22:21:15.642]        </control>
[22:21:15.642]        // end if-block "isSWJ"
[22:21:15.642]      </control>
[22:21:15.643]      <control if="!isSWJ" while="" timeout="0" info="">
[22:21:15.643]        // if-block "!isSWJ"
[22:21:15.643]          // =>  FALSE
[22:21:15.643]        // skip if-block "!isSWJ"
[22:21:15.643]      </control>
[22:21:15.643]      <block atomic="false" info="">
[22:21:15.643]        ReadDP(0x0);
[22:21:15.662]  
[22:21:15.662]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:21:15.662]  
[22:21:15.664]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:21:15.664]      </block>
[22:21:15.664]      // end if-block "protType == 2"
[22:21:15.664]    </control>
[22:21:15.664]  </sequence>
[22:21:15.664]  
[22:22:05.170]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:22:05.170]  
[22:22:05.171]  <debugvars>
[22:22:05.171]    // Pre-defined
[22:22:05.171]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:22:05.171]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:22:05.171]    __dp=0x00000000
[22:22:05.171]    __ap=0x00000000
[22:22:05.171]    __traceout=0x00000000      (Trace Disabled)
[22:22:05.171]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:22:05.171]    __FlashAddr=0x00000000
[22:22:05.171]    __FlashLen=0x00000000
[22:22:05.172]    __FlashArg=0x00000000
[22:22:05.172]    __FlashOp=0x00000000
[22:22:05.172]    __Result=0x00000000
[22:22:05.172]  </debugvars>
[22:22:05.172]  
[22:22:05.172]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:22:05.172]    <block atomic="false" info="">
[22:22:05.172]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:22:05.172]        // -> [isSWJ <= 0x00000001]
[22:22:05.172]      __var hasDormant = __protocol & 0x00020000;
[22:22:05.172]        // -> [hasDormant <= 0x00000000]
[22:22:05.173]      __var protType   = __protocol & 0x0000FFFF;
[22:22:05.173]        // -> [protType <= 0x00000002]
[22:22:05.173]    </block>
[22:22:05.173]    <control if="protType == 1" while="" timeout="0" info="">
[22:22:05.173]      // if-block "protType == 1"
[22:22:05.173]        // =>  FALSE
[22:22:05.173]      // skip if-block "protType == 1"
[22:22:05.173]    </control>
[22:22:05.173]    <control if="protType == 2" while="" timeout="0" info="">
[22:22:05.173]      // if-block "protType == 2"
[22:22:05.173]        // =>  TRUE
[22:22:05.173]      <control if="isSWJ" while="" timeout="0" info="">
[22:22:05.174]        // if-block "isSWJ"
[22:22:05.174]          // =>  TRUE
[22:22:05.174]        <control if="hasDormant" while="" timeout="0" info="">
[22:22:05.174]          // if-block "hasDormant"
[22:22:05.175]            // =>  FALSE
[22:22:05.175]          // skip if-block "hasDormant"
[22:22:05.175]        </control>
[22:22:05.175]        <control if="!hasDormant" while="" timeout="0" info="">
[22:22:05.175]          // if-block "!hasDormant"
[22:22:05.175]            // =>  TRUE
[22:22:05.175]          <block atomic="false" info="">
[22:22:05.175]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:22:05.179]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:05.179]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:22:05.181]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:22:05.181]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:22:05.183]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:05.183]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:22:05.185]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:22:05.185]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:22:05.187]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:05.187]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:22:05.188]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:22:05.189]          </block>
[22:22:05.189]          // end if-block "!hasDormant"
[22:22:05.189]        </control>
[22:22:05.189]        // end if-block "isSWJ"
[22:22:05.189]      </control>
[22:22:05.189]      <control if="!isSWJ" while="" timeout="0" info="">
[22:22:05.189]        // if-block "!isSWJ"
[22:22:05.189]          // =>  FALSE
[22:22:05.189]        // skip if-block "!isSWJ"
[22:22:05.190]      </control>
[22:22:05.190]      <block atomic="false" info="">
[22:22:05.190]        ReadDP(0x0);
[22:22:05.191]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:22:05.192]      </block>
[22:22:05.192]      // end if-block "protType == 2"
[22:22:05.192]    </control>
[22:22:05.192]  </sequence>
[22:22:05.192]  
[22:22:05.196]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:22:05.196]  
[22:22:05.196]  <debugvars>
[22:22:05.196]    // Pre-defined
[22:22:05.196]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:22:05.196]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:22:05.197]    __dp=0x00000000
[22:22:05.197]    __ap=0x00000000
[22:22:05.197]    __traceout=0x00000000      (Trace Disabled)
[22:22:05.197]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:22:05.197]    __FlashAddr=0x00000000
[22:22:05.197]    __FlashLen=0x00000000
[22:22:05.197]    __FlashArg=0x00000000
[22:22:05.197]    __FlashOp=0x00000000
[22:22:05.197]    __Result=0x00000000
[22:22:05.197]  </debugvars>
[22:22:05.197]  
[22:22:05.198]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:22:05.198]    <block atomic="false" info="">
[22:22:05.198]      __var SW_DP_ABORT       = 0x0;
[22:22:05.198]        // -> [SW_DP_ABORT <= 0x00000000]
[22:22:05.198]      __var DP_CTRL_STAT      = 0x4;
[22:22:05.198]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:22:05.198]      __var DP_SELECT         = 0x8;
[22:22:05.198]        // -> [DP_SELECT <= 0x00000008]
[22:22:05.198]      __var powered_down      = 0;
[22:22:05.198]        // -> [powered_down <= 0x00000000]
[22:22:05.198]      WriteDP(DP_SELECT, 0x00000000);
[22:22:05.200]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:22:05.200]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:22:05.202]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:22:05.202]        // -> [powered_down <= 0x00000001]
[22:22:05.202]    </block>
[22:22:05.202]    <control if="powered_down" while="" timeout="0" info="">
[22:22:05.202]      // if-block "powered_down"
[22:22:05.203]        // =>  TRUE
[22:22:05.203]      <block atomic="false" info="">
[22:22:05.203]        Message(0, "Debug/System power-up request sent");
[22:22:05.204]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:22:05.206]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:22:05.206]      </block>
[22:22:05.206]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:22:05.206]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.209]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.209]        // while-condition  =>  TRUE
[22:22:05.209]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.211]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.211]        // while-condition  =>  TRUE
[22:22:05.211]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.213]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.213]        // while-condition  =>  TRUE
[22:22:05.213]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.215]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.215]        // while-condition  =>  TRUE
[22:22:05.215]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.217]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.217]        // while-condition  =>  TRUE
[22:22:05.217]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.219]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.219]        // while-condition  =>  TRUE
[22:22:05.219]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.220]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.221]        // while-condition  =>  TRUE
[22:22:05.221]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.222]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.222]        // while-condition  =>  TRUE
[22:22:05.223]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.224]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.224]        // while-condition  =>  TRUE
[22:22:05.224]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.226]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.226]        // while-condition  =>  TRUE
[22:22:05.226]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.228]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.228]        // while-condition  =>  TRUE
[22:22:05.228]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.230]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.230]        // while-condition  =>  TRUE
[22:22:05.230]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.232]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.232]        // while-condition  =>  TRUE
[22:22:05.232]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.234]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.234]        // while-condition  =>  TRUE
[22:22:05.234]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.236]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.236]        // while-condition  =>  TRUE
[22:22:05.236]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.239]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.239]        // while-condition  =>  TRUE
[22:22:05.239]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.240]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.241]        // while-condition  =>  TRUE
[22:22:05.241]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.242]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.243]        // while-condition  =>  TRUE
[22:22:05.243]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.244]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.245]        // while-condition  =>  TRUE
[22:22:05.245]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.246]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.247]        // while-condition  =>  TRUE
[22:22:05.247]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.248]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.249]        // while-condition  =>  TRUE
[22:22:05.249]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.250]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.250]        // while-condition  =>  TRUE
[22:22:05.251]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.252]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.253]        // while-condition  =>  TRUE
[22:22:05.253]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.254]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.255]        // while-condition  =>  TRUE
[22:22:05.255]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.257]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.257]        // while-condition  =>  TRUE
[22:22:05.258]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.260]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.260]        // while-condition  =>  TRUE
[22:22:05.260]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.262]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.262]        // while-condition  =>  TRUE
[22:22:05.262]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.263]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.264]        // while-condition  =>  TRUE
[22:22:05.264]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.265]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.266]        // while-condition  =>  TRUE
[22:22:05.266]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.267]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.268]        // while-condition  =>  TRUE
[22:22:05.268]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.269]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.270]        // while-condition  =>  TRUE
[22:22:05.270]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.272]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.272]        // while-condition  =>  TRUE
[22:22:05.272]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.274]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.274]        // while-condition  =>  TRUE
[22:22:05.274]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.276]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.276]        // while-condition  =>  TRUE
[22:22:05.276]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.279]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.279]        // while-condition  =>  TRUE
[22:22:05.279]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.281]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.281]        // while-condition  =>  TRUE
[22:22:05.281]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.282]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.282]        // while-condition  =>  TRUE
[22:22:05.282]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.284]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.284]        // while-condition  =>  TRUE
[22:22:05.284]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.286]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.286]        // while-condition  =>  TRUE
[22:22:05.286]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.289]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.289]        // while-condition  =>  TRUE
[22:22:05.289]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.291]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.291]        // while-condition  =>  TRUE
[22:22:05.291]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.294]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.294]        // while-condition  =>  TRUE
[22:22:05.294]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.296]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.296]        // while-condition  =>  TRUE
[22:22:05.296]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.298]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.298]        // while-condition  =>  TRUE
[22:22:05.298]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.299]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.300]        // while-condition  =>  TRUE
[22:22:05.300]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.301]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.301]        // while-condition  =>  TRUE
[22:22:05.301]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.303]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.303]        // while-condition  =>  TRUE
[22:22:05.303]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.305]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.305]        // while-condition  =>  TRUE
[22:22:05.305]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.307]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.307]        // while-condition  =>  TRUE
[22:22:05.307]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.309]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.309]        // while-condition  =>  TRUE
[22:22:05.309]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.311]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.311]        // while-condition  =>  TRUE
[22:22:05.311]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.314]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.314]        // while-condition  =>  TRUE
[22:22:05.314]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.316]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.316]        // while-condition  =>  TRUE
[22:22:05.316]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.317]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.318]        // while-condition  =>  TRUE
[22:22:05.318]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.319]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.320]        // while-condition  =>  TRUE
[22:22:05.320]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.322]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.322]        // while-condition  =>  TRUE
[22:22:05.322]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.324]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.325]        // while-condition  =>  TRUE
[22:22:05.325]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.326]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.326]        // while-condition  =>  TRUE
[22:22:05.326]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.328]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.328]        // while-condition  =>  TRUE
[22:22:05.328]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.330]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.330]        // while-condition  =>  TRUE
[22:22:05.330]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.332]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.332]        // while-condition  =>  TRUE
[22:22:05.332]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.334]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.334]        // while-condition  =>  TRUE
[22:22:05.334]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.336]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.336]        // while-condition  =>  TRUE
[22:22:05.336]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.338]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.338]        // while-condition  =>  TRUE
[22:22:05.338]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.341]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.341]        // while-condition  =>  TRUE
[22:22:05.341]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.343]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.343]        // while-condition  =>  TRUE
[22:22:05.343]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.345]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.345]        // while-condition  =>  TRUE
[22:22:05.345]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.347]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.347]        // while-condition  =>  TRUE
[22:22:05.347]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.349]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.349]        // while-condition  =>  TRUE
[22:22:05.349]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.350]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.351]        // while-condition  =>  TRUE
[22:22:05.351]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.352]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.353]        // while-condition  =>  TRUE
[22:22:05.353]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.354]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.355]        // while-condition  =>  TRUE
[22:22:05.355]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.356]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.356]        // while-condition  =>  TRUE
[22:22:05.357]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.358]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.359]        // while-condition  =>  TRUE
[22:22:05.359]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.360]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.360]        // while-condition  =>  TRUE
[22:22:05.360]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.362]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.362]        // while-condition  =>  TRUE
[22:22:05.362]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.364]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.364]        // while-condition  =>  TRUE
[22:22:05.364]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.366]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.366]        // while-condition  =>  TRUE
[22:22:05.366]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.368]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.368]        // while-condition  =>  TRUE
[22:22:05.368]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.370]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.370]        // while-condition  =>  TRUE
[22:22:05.370]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.373]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.373]        // while-condition  =>  TRUE
[22:22:05.373]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.375]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.375]        // while-condition  =>  TRUE
[22:22:05.375]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.377]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.377]        // while-condition  =>  TRUE
[22:22:05.377]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.379]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.379]        // while-condition  =>  TRUE
[22:22:05.379]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.380]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.380]        // while-condition  =>  TRUE
[22:22:05.381]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.382]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.382]        // while-condition  =>  TRUE
[22:22:05.382]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.384]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.384]        // while-condition  =>  TRUE
[22:22:05.385]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.386]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.386]        // while-condition  =>  TRUE
[22:22:05.386]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.388]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.388]        // while-condition  =>  TRUE
[22:22:05.388]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.390]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.390]        // while-condition  =>  TRUE
[22:22:05.390]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.392]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.392]        // while-condition  =>  TRUE
[22:22:05.392]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.395]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.395]        // while-condition  =>  TRUE
[22:22:05.395]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.397]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.397]        // while-condition  =>  TRUE
[22:22:05.397]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.399]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.399]        // while-condition  =>  TRUE
[22:22:05.399]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.401]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.401]        // while-condition  =>  TRUE
[22:22:05.401]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.403]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.403]        // while-condition  =>  TRUE
[22:22:05.403]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.405]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.405]        // while-condition  =>  TRUE
[22:22:05.405]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.407]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.407]        // while-condition  =>  TRUE
[22:22:05.407]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.409]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.409]        // while-condition  =>  TRUE
[22:22:05.409]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.410]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.411]        // while-condition  =>  TRUE
[22:22:05.411]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.412]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.413]        // while-condition  =>  TRUE
[22:22:05.413]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.414]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.415]        // while-condition  =>  TRUE
[22:22:05.415]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.416]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.417]        // while-condition  =>  TRUE
[22:22:05.417]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.418]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.418]        // while-condition  =>  TRUE
[22:22:05.419]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.420]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.420]        // while-condition  =>  TRUE
[22:22:05.421]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.422]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.422]        // while-condition  =>  TRUE
[22:22:05.423]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.424]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.424]        // while-condition  =>  TRUE
[22:22:05.424]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.426]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.426]        // while-condition  =>  TRUE
[22:22:05.426]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.428]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.428]        // while-condition  =>  TRUE
[22:22:05.428]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.430]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.430]        // while-condition  =>  TRUE
[22:22:05.430]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.432]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.432]        // while-condition  =>  TRUE
[22:22:05.432]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.434]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.434]        // while-condition  =>  TRUE
[22:22:05.434]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.436]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.436]        // while-condition  =>  TRUE
[22:22:05.436]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.438]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.438]        // while-condition  =>  TRUE
[22:22:05.438]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.441]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.441]        // while-condition  =>  TRUE
[22:22:05.441]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.443]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.443]        // while-condition  =>  TRUE
[22:22:05.443]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.445]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.445]        // while-condition  =>  TRUE
[22:22:05.445]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.447]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.447]        // while-condition  =>  TRUE
[22:22:05.447]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.448]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.449]        // while-condition  =>  TRUE
[22:22:05.449]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.450]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.450]        // while-condition  =>  TRUE
[22:22:05.451]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.452]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.453]        // while-condition  =>  TRUE
[22:22:05.453]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.454]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.454]        // while-condition  =>  TRUE
[22:22:05.454]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.456]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.456]        // while-condition  =>  TRUE
[22:22:05.456]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.458]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.458]        // while-condition  =>  TRUE
[22:22:05.458]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.460]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.461]        // while-condition  =>  TRUE
[22:22:05.461]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.462]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.462]        // while-condition  =>  TRUE
[22:22:05.462]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.464]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.464]        // while-condition  =>  TRUE
[22:22:05.464]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.466]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.466]        // while-condition  =>  TRUE
[22:22:05.466]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.468]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.468]        // while-condition  =>  TRUE
[22:22:05.468]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.470]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.470]        // while-condition  =>  TRUE
[22:22:05.470]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.473]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.473]        // while-condition  =>  TRUE
[22:22:05.473]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.475]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.475]        // while-condition  =>  TRUE
[22:22:05.475]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.477]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.477]        // while-condition  =>  TRUE
[22:22:05.477]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.478]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.479]        // while-condition  =>  TRUE
[22:22:05.479]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.480]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.481]        // while-condition  =>  TRUE
[22:22:05.481]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.482]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.483]        // while-condition  =>  TRUE
[22:22:05.483]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.484]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.485]        // while-condition  =>  TRUE
[22:22:05.485]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.486]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.486]        // while-condition  =>  TRUE
[22:22:05.486]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.488]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.488]        // while-condition  =>  TRUE
[22:22:05.488]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.490]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.490]        // while-condition  =>  TRUE
[22:22:05.490]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.492]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.492]        // while-condition  =>  TRUE
[22:22:05.492]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.494]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.494]        // while-condition  =>  TRUE
[22:22:05.494]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.496]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.496]        // while-condition  =>  TRUE
[22:22:05.496]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.499]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.499]        // while-condition  =>  TRUE
[22:22:05.499]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.501]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.501]        // while-condition  =>  TRUE
[22:22:05.501]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.503]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.503]        // while-condition  =>  TRUE
[22:22:05.503]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.505]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.506]        // while-condition  =>  TRUE
[22:22:05.506]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.507]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.508]        // while-condition  =>  TRUE
[22:22:05.508]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.509]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.509]        // while-condition  =>  TRUE
[22:22:05.510]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.511]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.512]        // while-condition  =>  TRUE
[22:22:05.512]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.513]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.513]        // while-condition  =>  TRUE
[22:22:05.514]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.515]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.516]        // while-condition  =>  TRUE
[22:22:05.516]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.517]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.517]        // while-condition  =>  TRUE
[22:22:05.517]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.519]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.519]        // while-condition  =>  TRUE
[22:22:05.519]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.521]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.521]        // while-condition  =>  TRUE
[22:22:05.521]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.524]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.524]        // while-condition  =>  TRUE
[22:22:05.524]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.525]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.526]        // while-condition  =>  TRUE
[22:22:05.526]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.527]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.528]        // while-condition  =>  TRUE
[22:22:05.528]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.529]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.530]        // while-condition  =>  TRUE
[22:22:05.530]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.531]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.531]        // while-condition  =>  TRUE
[22:22:05.531]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.533]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.533]        // while-condition  =>  TRUE
[22:22:05.533]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.535]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.535]        // while-condition  =>  TRUE
[22:22:05.535]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.537]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.537]        // while-condition  =>  TRUE
[22:22:05.537]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.539]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.539]        // while-condition  =>  TRUE
[22:22:05.539]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.542]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.542]        // while-condition  =>  TRUE
[22:22:05.542]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.544]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.544]        // while-condition  =>  TRUE
[22:22:05.544]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.546]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.546]        // while-condition  =>  TRUE
[22:22:05.546]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.548]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.548]        // while-condition  =>  TRUE
[22:22:05.548]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.549]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.550]        // while-condition  =>  TRUE
[22:22:05.550]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.551]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.552]        // while-condition  =>  TRUE
[22:22:05.552]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.553]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.553]        // while-condition  =>  TRUE
[22:22:05.554]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.555]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.555]        // while-condition  =>  TRUE
[22:22:05.556]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.557]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.557]        // while-condition  =>  TRUE
[22:22:05.557]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.560]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.560]        // while-condition  =>  TRUE
[22:22:05.560]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.561]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.561]        // while-condition  =>  TRUE
[22:22:05.562]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.563]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.563]        // while-condition  =>  TRUE
[22:22:05.564]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.565]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.565]        // while-condition  =>  TRUE
[22:22:05.565]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.568]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.568]        // while-condition  =>  TRUE
[22:22:05.568]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.570]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.570]        // while-condition  =>  TRUE
[22:22:05.570]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.572]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.572]        // while-condition  =>  TRUE
[22:22:05.572]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.573]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.573]        // while-condition  =>  TRUE
[22:22:05.574]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.575]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.575]        // while-condition  =>  TRUE
[22:22:05.575]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.577]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.577]        // while-condition  =>  TRUE
[22:22:05.577]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.579]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.579]        // while-condition  =>  TRUE
[22:22:05.580]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.581]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.581]        // while-condition  =>  TRUE
[22:22:05.581]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.583]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.583]        // while-condition  =>  TRUE
[22:22:05.583]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.586]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.586]        // while-condition  =>  TRUE
[22:22:05.586]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.587]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.588]        // while-condition  =>  TRUE
[22:22:05.588]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.589]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.590]        // while-condition  =>  TRUE
[22:22:05.590]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.591]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.592]        // while-condition  =>  TRUE
[22:22:05.592]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.593]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.594]        // while-condition  =>  TRUE
[22:22:05.594]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.595]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.595]        // while-condition  =>  TRUE
[22:22:05.595]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.597]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.597]        // while-condition  =>  TRUE
[22:22:05.598]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.599]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.599]        // while-condition  =>  TRUE
[22:22:05.599]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.601]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.601]        // while-condition  =>  TRUE
[22:22:05.601]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.603]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.603]        // while-condition  =>  TRUE
[22:22:05.603]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.606]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.606]        // while-condition  =>  TRUE
[22:22:05.606]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.608]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.608]        // while-condition  =>  TRUE
[22:22:05.608]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.610]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.610]        // while-condition  =>  TRUE
[22:22:05.610]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.611]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.612]        // while-condition  =>  TRUE
[22:22:05.612]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.613]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.613]        // while-condition  =>  TRUE
[22:22:05.614]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.615]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.615]        // while-condition  =>  TRUE
[22:22:05.616]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.617]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.617]        // while-condition  =>  TRUE
[22:22:05.617]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.619]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.619]        // while-condition  =>  TRUE
[22:22:05.619]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.621]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.621]        // while-condition  =>  TRUE
[22:22:05.621]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.623]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.623]        // while-condition  =>  TRUE
[22:22:05.623]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.625]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.625]        // while-condition  =>  TRUE
[22:22:05.625]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.629]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.629]        // while-condition  =>  TRUE
[22:22:05.629]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.631]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.631]        // while-condition  =>  TRUE
[22:22:05.631]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.633]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.633]        // while-condition  =>  TRUE
[22:22:05.633]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.634]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.635]        // while-condition  =>  TRUE
[22:22:05.635]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.636]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.636]        // while-condition  =>  TRUE
[22:22:05.637]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.638]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.638]        // while-condition  =>  TRUE
[22:22:05.639]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.640]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.640]        // while-condition  =>  TRUE
[22:22:05.640]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.642]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.642]        // while-condition  =>  TRUE
[22:22:05.642]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.644]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.644]        // while-condition  =>  TRUE
[22:22:05.644]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.646]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.646]        // while-condition  =>  TRUE
[22:22:05.646]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.649]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.649]        // while-condition  =>  TRUE
[22:22:05.649]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.651]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.651]        // while-condition  =>  TRUE
[22:22:05.651]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.652]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.652]        // while-condition  =>  TRUE
[22:22:05.653]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.654]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.654]        // while-condition  =>  TRUE
[22:22:05.655]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.656]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.656]        // while-condition  =>  TRUE
[22:22:05.656]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.658]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.658]        // while-condition  =>  TRUE
[22:22:05.658]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.660]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.660]        // while-condition  =>  TRUE
[22:22:05.661]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.662]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.663]        // while-condition  =>  TRUE
[22:22:05.663]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.664]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.664]        // while-condition  =>  TRUE
[22:22:05.665]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.666]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.666]        // while-condition  =>  TRUE
[22:22:05.666]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.668]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.668]        // while-condition  =>  TRUE
[22:22:05.668]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.670]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.670]        // while-condition  =>  TRUE
[22:22:05.670]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.672]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.672]        // while-condition  =>  TRUE
[22:22:05.672]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.674]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.674]        // while-condition  =>  TRUE
[22:22:05.674]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.676]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.676]        // while-condition  =>  TRUE
[22:22:05.676]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.678]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.678]        // while-condition  =>  TRUE
[22:22:05.678]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.681]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.681]        // while-condition  =>  TRUE
[22:22:05.681]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.683]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.683]        // while-condition  =>  TRUE
[22:22:05.683]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.685]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.685]        // while-condition  =>  TRUE
[22:22:05.685]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.687]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.687]        // while-condition  =>  TRUE
[22:22:05.687]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.689]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.689]        // while-condition  =>  TRUE
[22:22:05.689]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.690]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.691]        // while-condition  =>  TRUE
[22:22:05.691]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.693]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.694]        // while-condition  =>  TRUE
[22:22:05.694]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.696]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.697]        // while-condition  =>  TRUE
[22:22:05.697]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.698]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.699]        // while-condition  =>  TRUE
[22:22:05.699]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.700]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.700]        // while-condition  =>  TRUE
[22:22:05.700]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.702]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.702]        // while-condition  =>  TRUE
[22:22:05.702]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.704]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.704]        // while-condition  =>  TRUE
[22:22:05.704]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.707]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.707]        // while-condition  =>  TRUE
[22:22:05.707]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.709]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.709]        // while-condition  =>  TRUE
[22:22:05.709]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.711]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.711]        // while-condition  =>  TRUE
[22:22:05.711]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.712]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.713]        // while-condition  =>  TRUE
[22:22:05.713]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.714]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.714]        // while-condition  =>  TRUE
[22:22:05.714]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.716]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.716]        // while-condition  =>  TRUE
[22:22:05.716]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.718]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.718]        // while-condition  =>  TRUE
[22:22:05.718]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.720]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.720]        // while-condition  =>  TRUE
[22:22:05.720]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.722]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.723]        // while-condition  =>  TRUE
[22:22:05.723]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.724]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.725]        // while-condition  =>  TRUE
[22:22:05.725]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.726]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.727]        // while-condition  =>  TRUE
[22:22:05.727]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.728]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.729]        // while-condition  =>  TRUE
[22:22:05.729]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.731]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.731]        // while-condition  =>  TRUE
[22:22:05.731]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.733]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.733]        // while-condition  =>  TRUE
[22:22:05.733]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.735]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.735]        // while-condition  =>  TRUE
[22:22:05.735]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.737]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.737]        // while-condition  =>  TRUE
[22:22:05.737]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.740]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.740]        // while-condition  =>  TRUE
[22:22:05.740]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.741]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.742]        // while-condition  =>  TRUE
[22:22:05.742]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.743]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.743]        // while-condition  =>  TRUE
[22:22:05.743]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.745]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.746]        // while-condition  =>  TRUE
[22:22:05.746]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.747]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.747]        // while-condition  =>  TRUE
[22:22:05.747]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.749]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.749]        // while-condition  =>  TRUE
[22:22:05.749]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.751]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.751]        // while-condition  =>  TRUE
[22:22:05.751]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.753]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.753]        // while-condition  =>  TRUE
[22:22:05.753]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.755]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.755]        // while-condition  =>  TRUE
[22:22:05.755]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.758]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.758]        // while-condition  =>  TRUE
[22:22:05.758]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.760]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.760]        // while-condition  =>  TRUE
[22:22:05.760]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.762]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.762]        // while-condition  =>  TRUE
[22:22:05.762]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.764]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.764]        // while-condition  =>  TRUE
[22:22:05.764]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.765]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.766]        // while-condition  =>  TRUE
[22:22:05.766]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.767]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.767]        // while-condition  =>  TRUE
[22:22:05.767]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.769]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.769]        // while-condition  =>  TRUE
[22:22:05.770]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.771]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.771]        // while-condition  =>  TRUE
[22:22:05.771]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.773]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.773]        // while-condition  =>  TRUE
[22:22:05.773]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.775]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.775]        // while-condition  =>  TRUE
[22:22:05.775]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.777]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.777]        // while-condition  =>  TRUE
[22:22:05.777]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.779]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.779]        // while-condition  =>  TRUE
[22:22:05.779]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.781]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.781]        // while-condition  =>  TRUE
[22:22:05.781]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.784]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.784]        // while-condition  =>  TRUE
[22:22:05.784]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.785]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.786]        // while-condition  =>  TRUE
[22:22:05.786]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.787]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.787]        // while-condition  =>  TRUE
[22:22:05.787]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.789]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.789]        // while-condition  =>  TRUE
[22:22:05.789]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.791]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.791]        // while-condition  =>  TRUE
[22:22:05.791]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.794]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.794]        // while-condition  =>  TRUE
[22:22:05.794]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.796]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.796]        // while-condition  =>  TRUE
[22:22:05.796]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.798]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.798]        // while-condition  =>  TRUE
[22:22:05.798]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.799]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.799]        // while-condition  =>  TRUE
[22:22:05.800]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.801]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.801]        // while-condition  =>  TRUE
[22:22:05.801]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.803]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.803]        // while-condition  =>  TRUE
[22:22:05.803]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.805]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.805]        // while-condition  =>  TRUE
[22:22:05.805]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.807]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.807]        // while-condition  =>  TRUE
[22:22:05.807]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.809]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.809]        // while-condition  =>  TRUE
[22:22:05.809]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.811]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.811]        // while-condition  =>  TRUE
[22:22:05.811]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.813]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.813]        // while-condition  =>  TRUE
[22:22:05.813]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.816]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.816]        // while-condition  =>  TRUE
[22:22:05.816]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.818]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.818]        // while-condition  =>  TRUE
[22:22:05.818]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.819]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.820]        // while-condition  =>  TRUE
[22:22:05.820]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.821]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.822]        // while-condition  =>  TRUE
[22:22:05.822]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.823]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.824]        // while-condition  =>  TRUE
[22:22:05.824]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.825]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.825]        // while-condition  =>  TRUE
[22:22:05.826]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.827]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.827]        // while-condition  =>  TRUE
[22:22:05.828]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.829]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.830]        // while-condition  =>  TRUE
[22:22:05.830]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.832]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.832]        // while-condition  =>  TRUE
[22:22:05.832]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.834]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.834]        // while-condition  =>  TRUE
[22:22:05.834]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.836]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.836]        // while-condition  =>  TRUE
[22:22:05.836]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.839]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.839]        // while-condition  =>  TRUE
[22:22:05.839]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.841]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.841]        // while-condition  =>  TRUE
[22:22:05.841]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.842]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.843]        // while-condition  =>  TRUE
[22:22:05.843]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.844]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.844]        // while-condition  =>  TRUE
[22:22:05.844]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.846]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.846]        // while-condition  =>  TRUE
[22:22:05.846]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.848]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.848]        // while-condition  =>  TRUE
[22:22:05.848]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.850]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.850]        // while-condition  =>  TRUE
[22:22:05.850]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.852]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.852]        // while-condition  =>  TRUE
[22:22:05.852]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.854]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.854]        // while-condition  =>  TRUE
[22:22:05.854]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.856]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.856]        // while-condition  =>  TRUE
[22:22:05.856]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.858]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.858]        // while-condition  =>  TRUE
[22:22:05.858]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.860]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.861]        // while-condition  =>  TRUE
[22:22:05.861]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.862]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.862]        // while-condition  =>  TRUE
[22:22:05.862]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.864]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.864]        // while-condition  =>  TRUE
[22:22:05.864]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.866]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.866]        // while-condition  =>  TRUE
[22:22:05.866]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.868]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.868]        // while-condition  =>  TRUE
[22:22:05.868]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.870]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.870]        // while-condition  =>  TRUE
[22:22:05.870]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.872]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.872]        // while-condition  =>  TRUE
[22:22:05.872]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.874]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.874]        // while-condition  =>  TRUE
[22:22:05.874]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.876]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.877]        // while-condition  =>  TRUE
[22:22:05.877]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.878]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.878]        // while-condition  =>  TRUE
[22:22:05.878]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.881]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.881]        // while-condition  =>  TRUE
[22:22:05.881]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.883]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.883]        // while-condition  =>  TRUE
[22:22:05.883]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.884]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.885]        // while-condition  =>  TRUE
[22:22:05.885]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.886]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.887]        // while-condition  =>  TRUE
[22:22:05.887]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.888]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.889]        // while-condition  =>  TRUE
[22:22:05.889]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.890]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.891]        // while-condition  =>  TRUE
[22:22:05.891]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.892]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.892]        // while-condition  =>  TRUE
[22:22:05.892]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.894]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.894]        // while-condition  =>  TRUE
[22:22:05.894]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.896]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.896]        // while-condition  =>  TRUE
[22:22:05.896]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.898]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.898]        // while-condition  =>  TRUE
[22:22:05.898]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.901]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.901]        // while-condition  =>  TRUE
[22:22:05.901]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.903]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.903]        // while-condition  =>  TRUE
[22:22:05.903]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.905]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.905]        // while-condition  =>  TRUE
[22:22:05.905]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.906]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.906]        // while-condition  =>  TRUE
[22:22:05.906]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.908]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.908]        // while-condition  =>  TRUE
[22:22:05.908]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.910]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.910]        // while-condition  =>  TRUE
[22:22:05.910]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.912]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.912]        // while-condition  =>  TRUE
[22:22:05.912]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.914]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.914]        // while-condition  =>  TRUE
[22:22:05.914]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.917]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.917]        // while-condition  =>  TRUE
[22:22:05.917]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.919]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.919]        // while-condition  =>  TRUE
[22:22:05.919]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.921]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.921]        // while-condition  =>  TRUE
[22:22:05.921]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.923]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.923]        // while-condition  =>  TRUE
[22:22:05.923]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.924]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.924]        // while-condition  =>  TRUE
[22:22:05.924]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.926]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.926]        // while-condition  =>  TRUE
[22:22:05.926]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.928]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.928]        // while-condition  =>  TRUE
[22:22:05.928]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.930]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.930]        // while-condition  =>  TRUE
[22:22:05.930]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.932]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.932]        // while-condition  =>  TRUE
[22:22:05.932]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.934]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.934]        // while-condition  =>  TRUE
[22:22:05.934]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.936]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.936]        // while-condition  =>  TRUE
[22:22:05.936]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.939]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.939]        // while-condition  =>  TRUE
[22:22:05.939]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.941]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.941]        // while-condition  =>  TRUE
[22:22:05.941]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.943]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.943]        // while-condition  =>  TRUE
[22:22:05.943]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.944]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.945]        // while-condition  =>  TRUE
[22:22:05.945]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.946]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.947]        // while-condition  =>  TRUE
[22:22:05.947]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.948]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.948]        // while-condition  =>  TRUE
[22:22:05.949]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.950]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.950]        // while-condition  =>  TRUE
[22:22:05.951]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.952]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.952]        // while-condition  =>  TRUE
[22:22:05.953]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.954]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.954]        // while-condition  =>  TRUE
[22:22:05.954]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.957]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.957]        // while-condition  =>  TRUE
[22:22:05.957]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.959]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.959]        // while-condition  =>  TRUE
[22:22:05.959]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.960]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.960]        // while-condition  =>  TRUE
[22:22:05.960]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.962]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.962]        // while-condition  =>  TRUE
[22:22:05.962]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.964]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.964]        // while-condition  =>  TRUE
[22:22:05.964]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.966]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.966]        // while-condition  =>  TRUE
[22:22:05.966]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.968]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.968]        // while-condition  =>  TRUE
[22:22:05.968]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.970]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.970]        // while-condition  =>  TRUE
[22:22:05.970]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.972]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.972]        // while-condition  =>  TRUE
[22:22:05.972]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.975]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.975]        // while-condition  =>  TRUE
[22:22:05.975]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.977]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.977]        // while-condition  =>  TRUE
[22:22:05.977]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.979]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.979]        // while-condition  =>  TRUE
[22:22:05.979]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.982]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.982]        // while-condition  =>  TRUE
[22:22:05.982]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.983]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.984]        // while-condition  =>  TRUE
[22:22:05.984]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.985]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.985]        // while-condition  =>  TRUE
[22:22:05.985]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.987]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.987]        // while-condition  =>  TRUE
[22:22:05.987]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.989]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.989]        // while-condition  =>  TRUE
[22:22:05.989]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.991]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.991]        // while-condition  =>  TRUE
[22:22:05.991]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.993]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.993]        // while-condition  =>  TRUE
[22:22:05.993]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.996]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.996]        // while-condition  =>  TRUE
[22:22:05.996]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:05.998]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:05.998]        // while-condition  =>  TRUE
[22:22:05.998]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.000]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.000]        // while-condition  =>  TRUE
[22:22:06.000]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.001]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.002]        // while-condition  =>  TRUE
[22:22:06.002]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.003]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.004]        // while-condition  =>  TRUE
[22:22:06.004]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.005]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.006]        // while-condition  =>  TRUE
[22:22:06.006]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.007]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.007]        // while-condition  =>  TRUE
[22:22:06.008]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.009]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.009]        // while-condition  =>  TRUE
[22:22:06.010]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.011]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.011]        // while-condition  =>  TRUE
[22:22:06.012]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.013]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.013]        // while-condition  =>  TRUE
[22:22:06.014]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.015]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.015]        // while-condition  =>  TRUE
[22:22:06.016]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.017]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.017]        // while-condition  =>  TRUE
[22:22:06.018]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.019]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.019]        // while-condition  =>  TRUE
[22:22:06.019]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.021]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.021]        // while-condition  =>  TRUE
[22:22:06.021]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.024]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.024]        // while-condition  =>  TRUE
[22:22:06.024]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.026]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.026]        // while-condition  =>  TRUE
[22:22:06.026]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.028]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.028]        // while-condition  =>  TRUE
[22:22:06.028]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.029]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.030]        // while-condition  =>  TRUE
[22:22:06.030]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.031]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.031]        // while-condition  =>  TRUE
[22:22:06.031]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.033]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.033]        // while-condition  =>  TRUE
[22:22:06.033]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.035]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.035]        // while-condition  =>  TRUE
[22:22:06.035]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.037]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.037]        // while-condition  =>  TRUE
[22:22:06.037]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.039]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.039]        // while-condition  =>  TRUE
[22:22:06.039]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.041]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.041]        // while-condition  =>  TRUE
[22:22:06.041]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.043]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.043]        // while-condition  =>  TRUE
[22:22:06.043]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.045]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.045]        // while-condition  =>  TRUE
[22:22:06.045]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.048]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.048]        // while-condition  =>  TRUE
[22:22:06.048]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.050]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.050]        // while-condition  =>  TRUE
[22:22:06.050]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.052]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.052]        // while-condition  =>  TRUE
[22:22:06.052]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.054]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.054]        // while-condition  =>  TRUE
[22:22:06.054]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.055]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.056]        // while-condition  =>  TRUE
[22:22:06.056]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.057]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.057]        // while-condition  =>  TRUE
[22:22:06.057]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.059]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.059]        // while-condition  =>  TRUE
[22:22:06.059]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.061]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.061]        // while-condition  =>  TRUE
[22:22:06.061]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.063]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.063]        // while-condition  =>  TRUE
[22:22:06.063]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.066]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.066]        // while-condition  =>  TRUE
[22:22:06.066]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.068]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.068]        // while-condition  =>  TRUE
[22:22:06.068]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.070]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.070]        // while-condition  =>  TRUE
[22:22:06.070]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.072]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.072]        // while-condition  =>  TRUE
[22:22:06.072]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.074]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.074]        // while-condition  =>  TRUE
[22:22:06.074]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.077]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.077]        // while-condition  =>  TRUE
[22:22:06.077]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.078]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.079]        // while-condition  =>  TRUE
[22:22:06.079]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.081]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.082]        // while-condition  =>  TRUE
[22:22:06.082]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.084]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.084]        // while-condition  =>  TRUE
[22:22:06.084]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.086]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.086]        // while-condition  =>  TRUE
[22:22:06.086]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.089]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.089]        // while-condition  =>  TRUE
[22:22:06.089]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.090]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.091]        // while-condition  =>  TRUE
[22:22:06.091]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.093]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.094]        // while-condition  =>  TRUE
[22:22:06.094]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.095]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.095]        // while-condition  =>  TRUE
[22:22:06.096]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.097]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.097]        // while-condition  =>  TRUE
[22:22:06.097]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.099]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.099]        // while-condition  =>  TRUE
[22:22:06.099]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.101]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.101]        // while-condition  =>  TRUE
[22:22:06.101]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.103]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.103]        // while-condition  =>  TRUE
[22:22:06.103]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.105]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.105]        // while-condition  =>  TRUE
[22:22:06.105]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.107]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.107]        // while-condition  =>  TRUE
[22:22:06.107]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.109]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.109]        // while-condition  =>  TRUE
[22:22:06.109]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.111]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.111]        // while-condition  =>  TRUE
[22:22:06.111]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.113]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.113]        // while-condition  =>  TRUE
[22:22:06.113]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.115]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.115]        // while-condition  =>  TRUE
[22:22:06.115]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.117]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.117]        // while-condition  =>  TRUE
[22:22:06.117]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.120]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.120]        // while-condition  =>  TRUE
[22:22:06.120]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.122]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.122]        // while-condition  =>  TRUE
[22:22:06.122]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.123]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.124]        // while-condition  =>  TRUE
[22:22:06.124]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.126]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.126]        // while-condition  =>  TRUE
[22:22:06.126]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.128]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.128]        // while-condition  =>  TRUE
[22:22:06.128]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.130]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.130]        // while-condition  =>  TRUE
[22:22:06.130]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.132]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.132]        // while-condition  =>  TRUE
[22:22:06.132]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.134]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.134]        // while-condition  =>  TRUE
[22:22:06.134]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.136]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.136]        // while-condition  =>  TRUE
[22:22:06.136]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.138]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.138]        // while-condition  =>  TRUE
[22:22:06.138]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.140]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.140]        // while-condition  =>  TRUE
[22:22:06.140]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.142]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.142]        // while-condition  =>  TRUE
[22:22:06.142]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.145]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.145]        // while-condition  =>  TRUE
[22:22:06.145]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.147]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.147]        // while-condition  =>  TRUE
[22:22:06.147]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.149]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.149]        // while-condition  =>  TRUE
[22:22:06.149]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.150]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.151]        // while-condition  =>  TRUE
[22:22:06.151]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.152]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.152]        // while-condition  =>  TRUE
[22:22:06.153]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.154]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.154]        // while-condition  =>  TRUE
[22:22:06.155]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.157]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.157]        // while-condition  =>  TRUE
[22:22:06.157]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.159]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.159]        // while-condition  =>  TRUE
[22:22:06.159]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.162]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.162]        // while-condition  =>  TRUE
[22:22:06.162]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.164]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.164]        // while-condition  =>  TRUE
[22:22:06.164]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.166]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.166]        // while-condition  =>  TRUE
[22:22:06.166]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.167]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.168]        // while-condition  =>  TRUE
[22:22:06.168]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.169]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.170]        // while-condition  =>  TRUE
[22:22:06.170]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.171]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.171]        // while-condition  =>  TRUE
[22:22:06.172]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.173]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.173]        // while-condition  =>  TRUE
[22:22:06.173]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.175]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.175]        // while-condition  =>  TRUE
[22:22:06.175]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.177]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.177]        // while-condition  =>  TRUE
[22:22:06.177]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.179]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.179]        // while-condition  =>  TRUE
[22:22:06.179]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.181]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.181]        // while-condition  =>  TRUE
[22:22:06.181]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.184]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.184]        // while-condition  =>  TRUE
[22:22:06.184]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.186]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.186]        // while-condition  =>  TRUE
[22:22:06.186]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.187]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.187]        // while-condition  =>  TRUE
[22:22:06.188]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.189]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.189]        // while-condition  =>  TRUE
[22:22:06.190]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.191]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.191]        // while-condition  =>  TRUE
[22:22:06.192]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.193]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.193]        // while-condition  =>  TRUE
[22:22:06.193]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.195]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.195]        // while-condition  =>  TRUE
[22:22:06.195]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.197]          // -> [ReadDP(0x00000004) => 0x50000040]   (__dp=0x00000000)
[22:22:06.197]        // while-condition  =>  TRUE
[22:22:06.197]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:22:06.197]        // while  =>  TIMEOUT
[22:22:06.197]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:22:06.198]      </control>
[22:22:06.198]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:22:06.198]        // if-block "(__protocol & 0xFFFF) == 1"
[22:22:06.198]          // =>  FALSE
[22:22:06.198]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:22:06.198]      </control>
[22:22:06.198]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:22:06.198]        // if-block "(__protocol & 0xFFFF) == 2"
[22:22:06.198]          // =>  TRUE
[22:22:06.199]        <block atomic="false" info="">
[22:22:06.199]          Message(0, "executing SWD power up");
[22:22:06.200]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:22:06.202]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:22:06.202]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:22:06.204]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:22:06.204]        </block>
[22:22:06.204]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:22:06.204]      </control>
[22:22:06.204]      // end if-block "powered_down"
[22:22:06.204]    </control>
[22:22:06.204]    <block atomic="false" info="">
[22:22:06.204]      __var DEBUG_PORT_VAL    = 0;
[22:22:06.204]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:22:06.204]      __var ACCESS_POINT_VAL  = 0;
[22:22:06.204]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:22:06.206]      __ap = 1; 
[22:22:06.206]        // -> [__ap <= 0x00000001]
[22:22:06.206]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:22:06.225]  
[22:22:06.225]  !!! E310 : Debug access failed - cannot write value 0x00000004 to AP register 0x0000000C (AP '0x00000001'))
[22:22:06.225]  
[22:22:06.225]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:22:06.226]    </block>
[22:22:06.226]  </sequence>
[22:22:06.226]  
[22:22:13.000]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:22:13.000]  
[22:22:13.000]  <debugvars>
[22:22:13.000]    // Pre-defined
[22:22:13.000]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:22:13.000]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[22:22:13.000]    __dp=0x00000000
[22:22:13.000]    __ap=0x00000000
[22:22:13.000]    __traceout=0x00000000      (Trace Disabled)
[22:22:13.001]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:22:13.001]    __FlashAddr=0x00000000
[22:22:13.001]    __FlashLen=0x00000000
[22:22:13.001]    __FlashArg=0x00000000
[22:22:13.001]    __FlashOp=0x00000000
[22:22:13.001]    __Result=0x00000000
[22:22:13.001]  </debugvars>
[22:22:13.002]  
[22:22:13.002]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:22:13.002]    <block atomic="false" info="">
[22:22:13.002]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:22:13.002]        // -> [isSWJ <= 0x00000001]
[22:22:13.002]      __var hasDormant = __protocol & 0x00020000;
[22:22:13.002]        // -> [hasDormant <= 0x00000000]
[22:22:13.003]      __var protType   = __protocol & 0x0000FFFF;
[22:22:13.003]        // -> [protType <= 0x00000002]
[22:22:13.003]    </block>
[22:22:13.003]    <control if="protType == 1" while="" timeout="0" info="">
[22:22:13.003]      // if-block "protType == 1"
[22:22:13.003]        // =>  FALSE
[22:22:13.003]      // skip if-block "protType == 1"
[22:22:13.003]    </control>
[22:22:13.003]    <control if="protType == 2" while="" timeout="0" info="">
[22:22:13.003]      // if-block "protType == 2"
[22:22:13.003]        // =>  TRUE
[22:22:13.003]      <control if="isSWJ" while="" timeout="0" info="">
[22:22:13.003]        // if-block "isSWJ"
[22:22:13.003]          // =>  TRUE
[22:22:13.004]        <control if="hasDormant" while="" timeout="0" info="">
[22:22:13.004]          // if-block "hasDormant"
[22:22:13.004]            // =>  FALSE
[22:22:13.004]          // skip if-block "hasDormant"
[22:22:13.004]        </control>
[22:22:13.004]        <control if="!hasDormant" while="" timeout="0" info="">
[22:22:13.004]          // if-block "!hasDormant"
[22:22:13.004]            // =>  TRUE
[22:22:13.004]          <block atomic="false" info="">
[22:22:13.004]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:22:13.006]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:13.007]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:22:13.008]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:22:13.009]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:22:13.010]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:13.010]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:22:13.012]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:22:13.012]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:22:13.014]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:13.014]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:22:13.016]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:22:13.016]          </block>
[22:22:13.016]          // end if-block "!hasDormant"
[22:22:13.016]        </control>
[22:22:13.016]        // end if-block "isSWJ"
[22:22:13.017]      </control>
[22:22:13.017]      <control if="!isSWJ" while="" timeout="0" info="">
[22:22:13.017]        // if-block "!isSWJ"
[22:22:13.017]          // =>  FALSE
[22:22:13.017]        // skip if-block "!isSWJ"
[22:22:13.017]      </control>
[22:22:13.017]      <block atomic="false" info="">
[22:22:13.017]        ReadDP(0x0);
[22:22:13.034]  
[22:22:13.034]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:22:13.034]  
[22:22:13.035]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:22:13.035]      </block>
[22:22:13.035]      // end if-block "protType == 2"
[22:22:13.035]    </control>
[22:22:13.035]  </sequence>
[22:22:13.035]  
[22:22:16.932]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:22:16.932]  
[22:22:16.932]  <debugvars>
[22:22:16.932]    // Pre-defined
[22:22:16.932]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:22:16.933]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:22:16.933]    __dp=0x00000000
[22:22:16.933]    __ap=0x00000000
[22:22:16.933]    __traceout=0x00000000      (Trace Disabled)
[22:22:16.933]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:22:16.933]    __FlashAddr=0x00000000
[22:22:16.933]    __FlashLen=0x00000000
[22:22:16.933]    __FlashArg=0x00000000
[22:22:16.933]    __FlashOp=0x00000000
[22:22:16.933]    __Result=0x00000000
[22:22:16.934]  </debugvars>
[22:22:16.934]  
[22:22:16.934]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:22:16.934]    <block atomic="false" info="">
[22:22:16.934]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:22:16.934]        // -> [isSWJ <= 0x00000001]
[22:22:16.934]      __var hasDormant = __protocol & 0x00020000;
[22:22:16.934]        // -> [hasDormant <= 0x00000000]
[22:22:16.934]      __var protType   = __protocol & 0x0000FFFF;
[22:22:16.934]        // -> [protType <= 0x00000002]
[22:22:16.935]    </block>
[22:22:16.935]    <control if="protType == 1" while="" timeout="0" info="">
[22:22:16.935]      // if-block "protType == 1"
[22:22:16.935]        // =>  FALSE
[22:22:16.935]      // skip if-block "protType == 1"
[22:22:16.935]    </control>
[22:22:16.935]    <control if="protType == 2" while="" timeout="0" info="">
[22:22:16.935]      // if-block "protType == 2"
[22:22:16.935]        // =>  TRUE
[22:22:16.935]      <control if="isSWJ" while="" timeout="0" info="">
[22:22:16.935]        // if-block "isSWJ"
[22:22:16.936]          // =>  TRUE
[22:22:16.936]        <control if="hasDormant" while="" timeout="0" info="">
[22:22:16.936]          // if-block "hasDormant"
[22:22:16.936]            // =>  FALSE
[22:22:16.936]          // skip if-block "hasDormant"
[22:22:16.936]        </control>
[22:22:16.936]        <control if="!hasDormant" while="" timeout="0" info="">
[22:22:16.936]          // if-block "!hasDormant"
[22:22:16.936]            // =>  TRUE
[22:22:16.936]          <block atomic="false" info="">
[22:22:16.936]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:22:16.938]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:16.938]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:22:16.941]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:22:16.941]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:22:16.943]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:16.943]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:22:16.944]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:22:16.944]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:22:16.946]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:22:16.946]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:22:16.948]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:22:16.948]          </block>
[22:22:16.948]          // end if-block "!hasDormant"
[22:22:16.948]        </control>
[22:22:16.948]        // end if-block "isSWJ"
[22:22:16.948]      </control>
[22:22:16.948]      <control if="!isSWJ" while="" timeout="0" info="">
[22:22:16.948]        // if-block "!isSWJ"
[22:22:16.948]          // =>  FALSE
[22:22:16.948]        // skip if-block "!isSWJ"
[22:22:16.948]      </control>
[22:22:16.949]      <block atomic="false" info="">
[22:22:16.949]        ReadDP(0x0);
[22:22:16.961]  
[22:22:16.961]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:22:16.961]  
[22:22:16.962]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:22:16.962]      </block>
[22:22:16.963]      // end if-block "protType == 2"
[22:22:16.963]    </control>
[22:22:16.963]  </sequence>
[22:22:16.963]  
[22:24:28.432]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:24:28.432]  
[22:24:28.433]  <debugvars>
[22:24:28.433]    // Pre-defined
[22:24:28.433]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:24:28.433]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:24:28.433]    __dp=0x00000000
[22:24:28.433]    __ap=0x00000000
[22:24:28.433]    __traceout=0x00000000      (Trace Disabled)
[22:24:28.433]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:24:28.434]    __FlashAddr=0x00000000
[22:24:28.434]    __FlashLen=0x00000000
[22:24:28.434]    __FlashArg=0x00000000
[22:24:28.434]    __FlashOp=0x00000000
[22:24:28.434]    __Result=0x00000000
[22:24:28.434]  </debugvars>
[22:24:28.434]  
[22:24:28.434]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:24:28.434]    <block atomic="false" info="">
[22:24:28.435]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:24:28.435]        // -> [isSWJ <= 0x00000001]
[22:24:28.435]      __var hasDormant = __protocol & 0x00020000;
[22:24:28.435]        // -> [hasDormant <= 0x00000000]
[22:24:28.435]      __var protType   = __protocol & 0x0000FFFF;
[22:24:28.435]        // -> [protType <= 0x00000002]
[22:24:28.435]    </block>
[22:24:28.435]    <control if="protType == 1" while="" timeout="0" info="">
[22:24:28.435]      // if-block "protType == 1"
[22:24:28.435]        // =>  FALSE
[22:24:28.436]      // skip if-block "protType == 1"
[22:24:28.436]    </control>
[22:24:28.436]    <control if="protType == 2" while="" timeout="0" info="">
[22:24:28.436]      // if-block "protType == 2"
[22:24:28.436]        // =>  TRUE
[22:24:28.436]      <control if="isSWJ" while="" timeout="0" info="">
[22:24:28.436]        // if-block "isSWJ"
[22:24:28.436]          // =>  TRUE
[22:24:28.436]        <control if="hasDormant" while="" timeout="0" info="">
[22:24:28.436]          // if-block "hasDormant"
[22:24:28.436]            // =>  FALSE
[22:24:28.437]          // skip if-block "hasDormant"
[22:24:28.437]        </control>
[22:24:28.437]        <control if="!hasDormant" while="" timeout="0" info="">
[22:24:28.437]          // if-block "!hasDormant"
[22:24:28.437]            // =>  TRUE
[22:24:28.437]          <block atomic="false" info="">
[22:24:28.437]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:24:28.439]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:24:28.439]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:24:28.441]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:24:28.441]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:24:28.443]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:24:28.443]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:24:28.445]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:24:28.445]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:24:28.447]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:24:28.447]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:24:28.448]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:24:28.449]          </block>
[22:24:28.449]          // end if-block "!hasDormant"
[22:24:28.449]        </control>
[22:24:28.449]        // end if-block "isSWJ"
[22:24:28.449]      </control>
[22:24:28.449]      <control if="!isSWJ" while="" timeout="0" info="">
[22:24:28.449]        // if-block "!isSWJ"
[22:24:28.449]          // =>  FALSE
[22:24:28.449]        // skip if-block "!isSWJ"
[22:24:28.449]      </control>
[22:24:28.450]      <block atomic="false" info="">
[22:24:28.450]        ReadDP(0x0);
[22:24:28.463]  
[22:24:28.463]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:24:28.463]  
[22:24:28.464]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:24:28.464]      </block>
[22:24:28.464]      // end if-block "protType == 2"
[22:24:28.464]    </control>
[22:24:28.464]  </sequence>
[22:24:28.465]  
[22:25:43.835]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:25:43.835]  
[22:25:43.836]  <debugvars>
[22:25:43.836]    // Pre-defined
[22:25:43.836]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:25:43.836]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:25:43.836]    __dp=0x00000000
[22:25:43.837]    __ap=0x00000000
[22:25:43.837]    __traceout=0x00000000      (Trace Disabled)
[22:25:43.837]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:25:43.837]    __FlashAddr=0x00000000
[22:25:43.837]    __FlashLen=0x00000000
[22:25:43.837]    __FlashArg=0x00000000
[22:25:43.837]    __FlashOp=0x00000000
[22:25:43.837]    __Result=0x00000000
[22:25:43.837]  </debugvars>
[22:25:43.838]  
[22:25:43.838]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:25:43.838]    <block atomic="false" info="">
[22:25:43.838]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:25:43.838]        // -> [isSWJ <= 0x00000001]
[22:25:43.838]      __var hasDormant = __protocol & 0x00020000;
[22:25:43.838]        // -> [hasDormant <= 0x00000000]
[22:25:43.838]      __var protType   = __protocol & 0x0000FFFF;
[22:25:43.838]        // -> [protType <= 0x00000002]
[22:25:43.838]    </block>
[22:25:43.838]    <control if="protType == 1" while="" timeout="0" info="">
[22:25:43.838]      // if-block "protType == 1"
[22:25:43.839]        // =>  FALSE
[22:25:43.839]      // skip if-block "protType == 1"
[22:25:43.839]    </control>
[22:25:43.839]    <control if="protType == 2" while="" timeout="0" info="">
[22:25:43.839]      // if-block "protType == 2"
[22:25:43.839]        // =>  TRUE
[22:25:43.839]      <control if="isSWJ" while="" timeout="0" info="">
[22:25:43.839]        // if-block "isSWJ"
[22:25:43.839]          // =>  TRUE
[22:25:43.839]        <control if="hasDormant" while="" timeout="0" info="">
[22:25:43.839]          // if-block "hasDormant"
[22:25:43.839]            // =>  FALSE
[22:25:43.840]          // skip if-block "hasDormant"
[22:25:43.840]        </control>
[22:25:43.840]        <control if="!hasDormant" while="" timeout="0" info="">
[22:25:43.840]          // if-block "!hasDormant"
[22:25:43.840]            // =>  TRUE
[22:25:43.840]          <block atomic="false" info="">
[22:25:43.840]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:25:43.842]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:25:43.842]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:25:43.843]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:25:43.844]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:25:43.846]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:25:43.847]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:25:43.848]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:25:43.849]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:25:43.850]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:25:43.851]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:25:43.852]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:25:43.852]          </block>
[22:25:43.852]          // end if-block "!hasDormant"
[22:25:43.853]        </control>
[22:25:43.853]        // end if-block "isSWJ"
[22:25:43.853]      </control>
[22:25:43.853]      <control if="!isSWJ" while="" timeout="0" info="">
[22:25:43.853]        // if-block "!isSWJ"
[22:25:43.853]          // =>  FALSE
[22:25:43.853]        // skip if-block "!isSWJ"
[22:25:43.853]      </control>
[22:25:43.853]      <block atomic="false" info="">
[22:25:43.853]        ReadDP(0x0);
[22:25:43.855]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:25:43.855]      </block>
[22:25:43.855]      // end if-block "protType == 2"
[22:25:43.855]    </control>
[22:25:43.856]  </sequence>
[22:25:43.856]  
[22:25:43.859]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:25:43.859]  
[22:25:43.859]  <debugvars>
[22:25:43.859]    // Pre-defined
[22:25:43.859]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:25:43.859]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:25:43.859]    __dp=0x00000000
[22:25:43.859]    __ap=0x00000000
[22:25:43.859]    __traceout=0x00000000      (Trace Disabled)
[22:25:43.860]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:25:43.860]    __FlashAddr=0x00000000
[22:25:43.860]    __FlashLen=0x00000000
[22:25:43.861]    __FlashArg=0x00000000
[22:25:43.861]    __FlashOp=0x00000000
[22:25:43.861]    __Result=0x00000000
[22:25:43.861]  </debugvars>
[22:25:43.861]  
[22:25:43.861]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:25:43.861]    <block atomic="false" info="">
[22:25:43.861]      __var SW_DP_ABORT       = 0x0;
[22:25:43.861]        // -> [SW_DP_ABORT <= 0x00000000]
[22:25:43.861]      __var DP_CTRL_STAT      = 0x4;
[22:25:43.861]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:25:43.861]      __var DP_SELECT         = 0x8;
[22:25:43.861]        // -> [DP_SELECT <= 0x00000008]
[22:25:43.862]      __var powered_down      = 0;
[22:25:43.862]        // -> [powered_down <= 0x00000000]
[22:25:43.862]      WriteDP(DP_SELECT, 0x00000000);
[22:25:43.864]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:25:43.864]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:25:43.866]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:25:43.866]        // -> [powered_down <= 0x00000001]
[22:25:43.866]    </block>
[22:25:43.866]    <control if="powered_down" while="" timeout="0" info="">
[22:25:43.866]      // if-block "powered_down"
[22:25:43.866]        // =>  TRUE
[22:25:43.866]      <block atomic="false" info="">
[22:25:43.867]        Message(0, "Debug/System power-up request sent");
[22:25:43.868]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:25:43.869]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:25:43.869]      </block>
[22:25:43.869]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:25:43.869]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:25:43.871]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:25:43.871]        // while-condition  =>  FALSE
[22:25:43.871]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:25:43.871]      </control>
[22:25:43.871]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:25:43.871]        // if-block "(__protocol & 0xFFFF) == 1"
[22:25:43.871]          // =>  FALSE
[22:25:43.871]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:25:43.872]      </control>
[22:25:43.872]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:25:43.872]        // if-block "(__protocol & 0xFFFF) == 2"
[22:25:43.872]          // =>  TRUE
[22:25:43.872]        <block atomic="false" info="">
[22:25:43.872]          Message(0, "executing SWD power up");
[22:25:43.873]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:25:43.875]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:25:43.875]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:25:43.877]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:25:43.877]        </block>
[22:25:43.877]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:25:43.877]      </control>
[22:25:43.877]      // end if-block "powered_down"
[22:25:43.877]    </control>
[22:25:43.877]    <block atomic="false" info="">
[22:25:43.877]      __var DEBUG_PORT_VAL    = 0;
[22:25:43.877]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:25:43.878]      __var ACCESS_POINT_VAL  = 0;
[22:25:43.878]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:25:43.878]      __ap = 1; 
[22:25:43.878]        // -> [__ap <= 0x00000001]
[22:25:43.878]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:25:43.882]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:25:43.882]      __ap = 4;
[22:25:43.882]        // -> [__ap <= 0x00000004]
[22:25:43.883]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:25:43.886]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[22:25:43.886]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[22:25:43.886]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:25:43.887]    </block>
[22:25:43.888]    <block atomic="false" info="">
[22:25:43.888]      __var nReset = 0x80;
[22:25:43.888]        // -> [nReset <= 0x00000080]
[22:25:43.888]      __var canReadPins = 0;
[22:25:43.888]        // -> [canReadPins <= 0x00000000]
[22:25:43.888]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:25:43.890]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:25:43.890]        // -> [canReadPins <= 0x00000001]
[22:25:43.890]    </block>
[22:25:43.890]    <control if="" while="1" timeout="200" info="">
[22:25:43.890]      // while "1"  (timeout="200")
[22:25:43.890]      // while-condition  =>  TRUE
[22:25:43.890]      // while "1"  (timeout="200")
[22:25:43.890]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.891]      // while "1"  (timeout="200")
[22:25:43.891]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.892]      // while-condition  =>  TRUE
[22:25:43.892]      // while "1"  (timeout="200")
[22:25:43.893]      // while-condition  =>  TRUE
[22:25:43.893]      // while "1"  (timeout="200")
[22:25:43.893]      // while-condition  =>  TRUE
[22:25:43.893]      // while "1"  (timeout="200")
[22:25:43.893]      // while-condition  =>  TRUE
[22:25:43.893]      // while "1"  (timeout="200")
[22:25:43.893]      // while-condition  =>  TRUE
[22:25:43.893]      // while "1"  (timeout="200")
[22:25:43.893]      // while-condition  =>  TRUE
[22:25:43.893]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.894]      // while "1"  (timeout="200")
[22:25:43.894]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.895]      // while-condition  =>  TRUE
[22:25:43.895]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.896]      // while "1"  (timeout="200")
[22:25:43.896]      // while-condition  =>  TRUE
[22:25:43.897]      // while "1"  (timeout="200")
[22:25:43.897]      // while  =>  TIMEOUT
[22:25:43.897]      // end while "1"
[22:25:43.897]    </control>
[22:25:43.897]    <control if="canReadPins" while="" timeout="0" info="">
[22:25:43.897]      // if-block "canReadPins"
[22:25:43.897]        // =>  TRUE
[22:25:43.897]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:25:43.897]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:25:43.899]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:25:43.899]        // while-condition  =>  TRUE
[22:25:43.899]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:25:43.901]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:25:43.901]        // while-condition  =>  FALSE
[22:25:43.901]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:25:43.901]      </control>
[22:25:43.901]      // end if-block "canReadPins"
[22:25:43.901]    </control>
[22:25:43.901]    <control if="!canReadPins" while="" timeout="0" info="">
[22:25:43.902]      // if-block "!canReadPins"
[22:25:43.902]        // =>  FALSE
[22:25:43.902]      // skip if-block "!canReadPins"
[22:25:43.902]    </control>
[22:25:43.902]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:25:43.902]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:25:43.902]        // =>  TRUE
[22:25:43.902]      <block atomic="false" info="">
[22:25:43.902]        WriteAP(0x00, 0x190008);
[22:25:43.903]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[22:25:43.904]        WriteAP(0xF0, 0x01);
[22:25:43.908]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:25:43.908]      </block>
[22:25:43.908]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:25:43.909]    </control>
[22:25:43.909]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:25:43.909]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:25:43.909]        // =>  FALSE
[22:25:43.909]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:25:43.909]    </control>
[22:25:43.909]    <block atomic="false" info="">
[22:25:43.909]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:25:43.913]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:25:43.913]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:25:43.913]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:25:43.914]      __ap = 0; //lets make sure we reset the access point selection
[22:25:43.914]        // -> [__ap <= 0x00000000]
[22:25:43.914]    </block>
[22:25:43.914]  </sequence>
[22:25:43.914]  
[22:25:43.934]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:25:43.934]  
[22:25:43.935]  <debugvars>
[22:25:43.935]    // Pre-defined
[22:25:43.935]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:25:43.935]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:25:43.935]    __dp=0x00000000
[22:25:43.935]    __ap=0x00000000
[22:25:43.935]    __traceout=0x00000000      (Trace Disabled)
[22:25:43.935]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:25:43.935]    __FlashAddr=0x00000000
[22:25:43.935]    __FlashLen=0x00000000
[22:25:43.935]    __FlashArg=0x00000000
[22:25:43.936]    __FlashOp=0x00000000
[22:25:43.936]    __Result=0x00000000
[22:25:43.936]  </debugvars>
[22:25:43.936]  
[22:25:43.936]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:25:43.936]    <block atomic="false" info="">
[22:25:43.936]      __var deviceID = 0;
[22:25:43.936]        // -> [deviceID <= 0x00000000]
[22:25:43.936]      __var version = 0;
[22:25:43.936]        // -> [version <= 0x00000000]
[22:25:43.937]      __var partNum = 0;
[22:25:43.937]        // -> [partNum <= 0x00000000]
[22:25:43.937]      __var manuf = 0;
[22:25:43.937]        // -> [manuf <= 0x00000000]
[22:25:43.937]      __var isMSPM0G1X0X_G3X0X = 0;
[22:25:43.937]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:25:43.937]      __var isProduction = 0;
[22:25:43.937]        // -> [isProduction <= 0x00000000]
[22:25:43.937]      __var continueId = 0;
[22:25:43.937]        // -> [continueId <= 0x00000000]
[22:25:43.937]      deviceID =   Read32(0x41C40004);
[22:25:43.943]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:25:43.943]        // -> [deviceID <= 0x2BB8802F]
[22:25:43.943]      version = deviceID >> 28;
[22:25:43.943]        // -> [version <= 0x00000002]
[22:25:43.943]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:25:43.943]        // -> [partNum <= 0x0000BB88]
[22:25:43.943]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:25:43.943]        // -> [manuf <= 0x00000017]
[22:25:43.944]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:25:43.944]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:25:43.944]      isProduction = (version > 0);
[22:25:43.944]        // -> [isProduction <= 0x00000001]
[22:25:43.944]    </block>
[22:25:43.944]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:25:43.944]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:25:43.944]        // =>  FALSE
[22:25:43.944]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:25:43.944]    </control>
[22:25:43.944]    <control if="continueId == 4" while="" timeout="0" info="">
[22:25:43.944]      // if-block "continueId == 4"
[22:25:43.944]        // =>  FALSE
[22:25:43.944]      // skip if-block "continueId == 4"
[22:25:43.945]    </control>
[22:25:43.945]    <control if="!isProduction" while="" timeout="0" info="">
[22:25:43.945]      // if-block "!isProduction"
[22:25:43.945]        // =>  FALSE
[22:25:43.945]      // skip if-block "!isProduction"
[22:25:43.945]    </control>
[22:25:43.945]  </sequence>
[22:25:43.945]  
[22:33:30.850]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:33:30.850]  
[22:33:30.850]  <debugvars>
[22:33:30.851]    // Pre-defined
[22:33:30.851]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:33:30.851]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:33:30.851]    __dp=0x00000000
[22:33:30.852]    __ap=0x00000000
[22:33:30.852]    __traceout=0x00000000      (Trace Disabled)
[22:33:30.852]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:33:30.852]    __FlashAddr=0x00000000
[22:33:30.852]    __FlashLen=0x00000000
[22:33:30.852]    __FlashArg=0x00000000
[22:33:30.852]    __FlashOp=0x00000000
[22:33:30.852]    __Result=0x00000000
[22:33:30.852]  </debugvars>
[22:33:30.852]  
[22:33:30.852]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:33:30.852]    <block atomic="false" info="">
[22:33:30.852]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:33:30.852]        // -> [isSWJ <= 0x00000001]
[22:33:30.853]      __var hasDormant = __protocol & 0x00020000;
[22:33:30.853]        // -> [hasDormant <= 0x00000000]
[22:33:30.853]      __var protType   = __protocol & 0x0000FFFF;
[22:33:30.853]        // -> [protType <= 0x00000002]
[22:33:30.853]    </block>
[22:33:30.853]    <control if="protType == 1" while="" timeout="0" info="">
[22:33:30.853]      // if-block "protType == 1"
[22:33:30.853]        // =>  FALSE
[22:33:30.853]      // skip if-block "protType == 1"
[22:33:30.853]    </control>
[22:33:30.853]    <control if="protType == 2" while="" timeout="0" info="">
[22:33:30.853]      // if-block "protType == 2"
[22:33:30.853]        // =>  TRUE
[22:33:30.853]      <control if="isSWJ" while="" timeout="0" info="">
[22:33:30.853]        // if-block "isSWJ"
[22:33:30.854]          // =>  TRUE
[22:33:30.854]        <control if="hasDormant" while="" timeout="0" info="">
[22:33:30.854]          // if-block "hasDormant"
[22:33:30.854]            // =>  FALSE
[22:33:30.854]          // skip if-block "hasDormant"
[22:33:30.854]        </control>
[22:33:30.854]        <control if="!hasDormant" while="" timeout="0" info="">
[22:33:30.854]          // if-block "!hasDormant"
[22:33:30.854]            // =>  TRUE
[22:33:30.854]          <block atomic="false" info="">
[22:33:30.854]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:33:30.856]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:33:30.857]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:33:30.858]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:33:30.858]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:33:30.860]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:33:30.860]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:33:30.862]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:33:30.862]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:33:30.865]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:33:30.865]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:33:30.867]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:33:30.867]          </block>
[22:33:30.867]          // end if-block "!hasDormant"
[22:33:30.867]        </control>
[22:33:30.867]        // end if-block "isSWJ"
[22:33:30.867]      </control>
[22:33:30.867]      <control if="!isSWJ" while="" timeout="0" info="">
[22:33:30.867]        // if-block "!isSWJ"
[22:33:30.867]          // =>  FALSE
[22:33:30.868]        // skip if-block "!isSWJ"
[22:33:30.868]      </control>
[22:33:30.868]      <block atomic="false" info="">
[22:33:30.868]        ReadDP(0x0);
[22:33:30.870]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:33:30.870]      </block>
[22:33:30.870]      // end if-block "protType == 2"
[22:33:30.870]    </control>
[22:33:30.870]  </sequence>
[22:33:30.870]  
[22:33:30.873]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:33:30.873]  
[22:33:30.873]  <debugvars>
[22:33:30.873]    // Pre-defined
[22:33:30.873]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:33:30.874]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:33:30.874]    __dp=0x00000000
[22:33:30.874]    __ap=0x00000000
[22:33:30.874]    __traceout=0x00000000      (Trace Disabled)
[22:33:30.874]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:33:30.874]    __FlashAddr=0x00000000
[22:33:30.874]    __FlashLen=0x00000000
[22:33:30.874]    __FlashArg=0x00000000
[22:33:30.874]    __FlashOp=0x00000000
[22:33:30.874]    __Result=0x00000000
[22:33:30.874]  </debugvars>
[22:33:30.874]  
[22:33:30.874]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:33:30.874]    <block atomic="false" info="">
[22:33:30.875]      __var SW_DP_ABORT       = 0x0;
[22:33:30.875]        // -> [SW_DP_ABORT <= 0x00000000]
[22:33:30.875]      __var DP_CTRL_STAT      = 0x4;
[22:33:30.875]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:33:30.875]      __var DP_SELECT         = 0x8;
[22:33:30.875]        // -> [DP_SELECT <= 0x00000008]
[22:33:30.875]      __var powered_down      = 0;
[22:33:30.875]        // -> [powered_down <= 0x00000000]
[22:33:30.875]      WriteDP(DP_SELECT, 0x00000000);
[22:33:30.877]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:33:30.877]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:33:30.879]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:33:30.879]        // -> [powered_down <= 0x00000001]
[22:33:30.879]    </block>
[22:33:30.879]    <control if="powered_down" while="" timeout="0" info="">
[22:33:30.880]      // if-block "powered_down"
[22:33:30.880]        // =>  TRUE
[22:33:30.880]      <block atomic="false" info="">
[22:33:30.880]        Message(0, "Debug/System power-up request sent");
[22:33:30.882]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:33:30.883]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:33:30.883]      </block>
[22:33:30.883]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:33:30.883]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:33:30.885]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:33:30.885]        // while-condition  =>  FALSE
[22:33:30.885]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:33:30.885]      </control>
[22:33:30.885]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:33:30.885]        // if-block "(__protocol & 0xFFFF) == 1"
[22:33:30.885]          // =>  FALSE
[22:33:30.886]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:33:30.886]      </control>
[22:33:30.886]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:33:30.886]        // if-block "(__protocol & 0xFFFF) == 2"
[22:33:30.886]          // =>  TRUE
[22:33:30.886]        <block atomic="false" info="">
[22:33:30.886]          Message(0, "executing SWD power up");
[22:33:30.887]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:33:30.890]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:33:30.890]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:33:30.892]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:33:30.892]        </block>
[22:33:30.892]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:33:30.892]      </control>
[22:33:30.892]      // end if-block "powered_down"
[22:33:30.892]    </control>
[22:33:30.892]    <block atomic="false" info="">
[22:33:30.892]      __var DEBUG_PORT_VAL    = 0;
[22:33:30.892]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:33:30.892]      __var ACCESS_POINT_VAL  = 0;
[22:33:30.892]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:33:30.892]      __ap = 1; 
[22:33:30.893]        // -> [__ap <= 0x00000001]
[22:33:30.893]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:33:30.897]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:33:30.897]      __ap = 4;
[22:33:30.897]        // -> [__ap <= 0x00000004]
[22:33:30.897]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:33:30.901]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[22:33:30.901]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[22:33:30.901]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:33:30.902]    </block>
[22:33:30.902]    <block atomic="false" info="">
[22:33:30.902]      __var nReset = 0x80;
[22:33:30.902]        // -> [nReset <= 0x00000080]
[22:33:30.902]      __var canReadPins = 0;
[22:33:30.902]        // -> [canReadPins <= 0x00000000]
[22:33:30.902]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:33:30.904]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:33:30.905]        // -> [canReadPins <= 0x00000001]
[22:33:30.905]    </block>
[22:33:30.905]    <control if="" while="1" timeout="200" info="">
[22:33:30.905]      // while "1"  (timeout="200")
[22:33:30.905]      // while-condition  =>  TRUE
[22:33:30.905]      // while "1"  (timeout="200")
[22:33:30.905]      // while-condition  =>  TRUE
[22:33:30.905]      // while "1"  (timeout="200")
[22:33:30.905]      // while-condition  =>  TRUE
[22:33:30.905]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.906]      // while "1"  (timeout="200")
[22:33:30.906]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.907]      // while-condition  =>  TRUE
[22:33:30.907]      // while "1"  (timeout="200")
[22:33:30.908]      // while-condition  =>  TRUE
[22:33:30.908]      // while "1"  (timeout="200")
[22:33:30.908]      // while  =>  TIMEOUT
[22:33:30.908]      // end while "1"
[22:33:30.908]    </control>
[22:33:30.908]    <control if="canReadPins" while="" timeout="0" info="">
[22:33:30.908]      // if-block "canReadPins"
[22:33:30.908]        // =>  TRUE
[22:33:30.908]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:33:30.908]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:33:30.910]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:33:30.910]        // while-condition  =>  TRUE
[22:33:30.911]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:33:30.912]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:33:30.913]        // while-condition  =>  FALSE
[22:33:30.913]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:33:30.913]      </control>
[22:33:30.913]      // end if-block "canReadPins"
[22:33:30.913]    </control>
[22:33:30.913]    <control if="!canReadPins" while="" timeout="0" info="">
[22:33:30.913]      // if-block "!canReadPins"
[22:33:30.913]        // =>  FALSE
[22:33:30.913]      // skip if-block "!canReadPins"
[22:33:30.913]    </control>
[22:33:30.913]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:33:30.913]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:33:30.913]        // =>  TRUE
[22:33:30.913]      <block atomic="false" info="">
[22:33:30.914]        WriteAP(0x00, 0x190008);
[22:33:30.915]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[22:33:30.915]        WriteAP(0xF0, 0x01);
[22:33:30.920]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:33:30.920]      </block>
[22:33:30.920]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:33:30.920]    </control>
[22:33:30.920]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:33:30.920]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:33:30.920]        // =>  FALSE
[22:33:30.920]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:33:30.920]    </control>
[22:33:30.920]    <block atomic="false" info="">
[22:33:30.920]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:33:30.923]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:33:30.924]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:33:30.924]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:33:30.925]      __ap = 0; //lets make sure we reset the access point selection
[22:33:30.925]        // -> [__ap <= 0x00000000]
[22:33:30.925]    </block>
[22:33:30.925]  </sequence>
[22:33:30.925]  
[22:33:30.945]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:33:30.945]  
[22:33:30.945]  <debugvars>
[22:33:30.945]    // Pre-defined
[22:33:30.946]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:33:30.946]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:33:30.946]    __dp=0x00000000
[22:33:30.946]    __ap=0x00000000
[22:33:30.946]    __traceout=0x00000000      (Trace Disabled)
[22:33:30.946]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:33:30.946]    __FlashAddr=0x00000000
[22:33:30.946]    __FlashLen=0x00000000
[22:33:30.946]    __FlashArg=0x00000000
[22:33:30.946]    __FlashOp=0x00000000
[22:33:30.946]    __Result=0x00000000
[22:33:30.946]  </debugvars>
[22:33:30.946]  
[22:33:30.946]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:33:30.946]    <block atomic="false" info="">
[22:33:30.947]      __var deviceID = 0;
[22:33:30.947]        // -> [deviceID <= 0x00000000]
[22:33:30.947]      __var version = 0;
[22:33:30.947]        // -> [version <= 0x00000000]
[22:33:30.947]      __var partNum = 0;
[22:33:30.947]        // -> [partNum <= 0x00000000]
[22:33:30.947]      __var manuf = 0;
[22:33:30.947]        // -> [manuf <= 0x00000000]
[22:33:30.947]      __var isMSPM0G1X0X_G3X0X = 0;
[22:33:30.947]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:33:30.947]      __var isProduction = 0;
[22:33:30.947]        // -> [isProduction <= 0x00000000]
[22:33:30.947]      __var continueId = 0;
[22:33:30.947]        // -> [continueId <= 0x00000000]
[22:33:30.947]      deviceID =   Read32(0x41C40004);
[22:33:30.955]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:33:30.955]        // -> [deviceID <= 0x2BB8802F]
[22:33:30.955]      version = deviceID >> 28;
[22:33:30.955]        // -> [version <= 0x00000002]
[22:33:30.955]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:33:30.955]        // -> [partNum <= 0x0000BB88]
[22:33:30.955]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:33:30.955]        // -> [manuf <= 0x00000017]
[22:33:30.955]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:33:30.955]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:33:30.955]      isProduction = (version > 0);
[22:33:30.955]        // -> [isProduction <= 0x00000001]
[22:33:30.956]    </block>
[22:33:30.956]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:33:30.956]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:33:30.956]        // =>  FALSE
[22:33:30.956]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:33:30.956]    </control>
[22:33:30.956]    <control if="continueId == 4" while="" timeout="0" info="">
[22:33:30.956]      // if-block "continueId == 4"
[22:33:30.956]        // =>  FALSE
[22:33:30.956]      // skip if-block "continueId == 4"
[22:33:30.956]    </control>
[22:33:30.956]    <control if="!isProduction" while="" timeout="0" info="">
[22:33:30.956]      // if-block "!isProduction"
[22:33:30.956]        // =>  FALSE
[22:33:30.956]      // skip if-block "!isProduction"
[22:33:30.957]    </control>
[22:33:30.957]  </sequence>
[22:33:30.957]  
[22:35:12.729]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:35:12.729]  
[22:35:12.731]  <debugvars>
[22:35:12.732]    // Pre-defined
[22:35:12.732]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:35:12.732]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:35:12.732]    __dp=0x00000000
[22:35:12.732]    __ap=0x00000000
[22:35:12.732]    __traceout=0x00000000      (Trace Disabled)
[22:35:12.732]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:35:12.732]    __FlashAddr=0x00000000
[22:35:12.732]    __FlashLen=0x00000000
[22:35:12.732]    __FlashArg=0x00000000
[22:35:12.732]    __FlashOp=0x00000000
[22:35:12.732]    __Result=0x00000000
[22:35:12.732]  </debugvars>
[22:35:12.733]  
[22:35:12.733]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:35:12.733]    <block atomic="false" info="">
[22:35:12.733]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:35:12.733]        // -> [isSWJ <= 0x00000001]
[22:35:12.733]      __var hasDormant = __protocol & 0x00020000;
[22:35:12.733]        // -> [hasDormant <= 0x00000000]
[22:35:12.733]      __var protType   = __protocol & 0x0000FFFF;
[22:35:12.733]        // -> [protType <= 0x00000002]
[22:35:12.734]    </block>
[22:35:12.734]    <control if="protType == 1" while="" timeout="0" info="">
[22:35:12.734]      // if-block "protType == 1"
[22:35:12.734]        // =>  FALSE
[22:35:12.734]      // skip if-block "protType == 1"
[22:35:12.734]    </control>
[22:35:12.734]    <control if="protType == 2" while="" timeout="0" info="">
[22:35:12.734]      // if-block "protType == 2"
[22:35:12.734]        // =>  TRUE
[22:35:12.734]      <control if="isSWJ" while="" timeout="0" info="">
[22:35:12.734]        // if-block "isSWJ"
[22:35:12.734]          // =>  TRUE
[22:35:12.734]        <control if="hasDormant" while="" timeout="0" info="">
[22:35:12.734]          // if-block "hasDormant"
[22:35:12.735]            // =>  FALSE
[22:35:12.735]          // skip if-block "hasDormant"
[22:35:12.735]        </control>
[22:35:12.735]        <control if="!hasDormant" while="" timeout="0" info="">
[22:35:12.735]          // if-block "!hasDormant"
[22:35:12.735]            // =>  TRUE
[22:35:12.735]          <block atomic="false" info="">
[22:35:12.735]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:35:12.738]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:35:12.738]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:35:12.740]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:35:12.740]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:35:12.742]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:35:12.742]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:35:12.744]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:35:12.744]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:35:12.746]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:35:12.746]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:35:12.748]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:35:12.748]          </block>
[22:35:12.748]          // end if-block "!hasDormant"
[22:35:12.748]        </control>
[22:35:12.748]        // end if-block "isSWJ"
[22:35:12.748]      </control>
[22:35:12.748]      <control if="!isSWJ" while="" timeout="0" info="">
[22:35:12.748]        // if-block "!isSWJ"
[22:35:12.748]          // =>  FALSE
[22:35:12.748]        // skip if-block "!isSWJ"
[22:35:12.749]      </control>
[22:35:12.749]      <block atomic="false" info="">
[22:35:12.749]        ReadDP(0x0);
[22:35:12.751]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:35:12.751]      </block>
[22:35:12.751]      // end if-block "protType == 2"
[22:35:12.751]    </control>
[22:35:12.751]  </sequence>
[22:35:12.751]  
[22:35:12.755]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:35:12.755]  
[22:35:12.755]  <debugvars>
[22:35:12.755]    // Pre-defined
[22:35:12.755]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:35:12.756]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:35:12.756]    __dp=0x00000000
[22:35:12.756]    __ap=0x00000000
[22:35:12.756]    __traceout=0x00000000      (Trace Disabled)
[22:35:12.756]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:35:12.756]    __FlashAddr=0x00000000
[22:35:12.756]    __FlashLen=0x00000000
[22:35:12.756]    __FlashArg=0x00000000
[22:35:12.756]    __FlashOp=0x00000000
[22:35:12.756]    __Result=0x00000000
[22:35:12.756]  </debugvars>
[22:35:12.756]  
[22:35:12.757]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:35:12.757]    <block atomic="false" info="">
[22:35:12.757]      __var SW_DP_ABORT       = 0x0;
[22:35:12.757]        // -> [SW_DP_ABORT <= 0x00000000]
[22:35:12.757]      __var DP_CTRL_STAT      = 0x4;
[22:35:12.757]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:35:12.757]      __var DP_SELECT         = 0x8;
[22:35:12.757]        // -> [DP_SELECT <= 0x00000008]
[22:35:12.757]      __var powered_down      = 0;
[22:35:12.757]        // -> [powered_down <= 0x00000000]
[22:35:12.757]      WriteDP(DP_SELECT, 0x00000000);
[22:35:12.759]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:35:12.759]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:35:12.762]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:35:12.762]        // -> [powered_down <= 0x00000001]
[22:35:12.762]    </block>
[22:35:12.762]    <control if="powered_down" while="" timeout="0" info="">
[22:35:12.762]      // if-block "powered_down"
[22:35:12.762]        // =>  TRUE
[22:35:12.762]      <block atomic="false" info="">
[22:35:12.763]        Message(0, "Debug/System power-up request sent");
[22:35:12.764]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:35:12.766]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:35:12.766]      </block>
[22:35:12.766]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:35:12.766]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:35:12.769]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:35:12.769]        // while-condition  =>  FALSE
[22:35:12.769]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:35:12.769]      </control>
[22:35:12.769]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:35:12.770]        // if-block "(__protocol & 0xFFFF) == 1"
[22:35:12.770]          // =>  FALSE
[22:35:12.770]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:35:12.770]      </control>
[22:35:12.770]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:35:12.770]        // if-block "(__protocol & 0xFFFF) == 2"
[22:35:12.770]          // =>  TRUE
[22:35:12.770]        <block atomic="false" info="">
[22:35:12.770]          Message(0, "executing SWD power up");
[22:35:12.771]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:35:12.773]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:35:12.773]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:35:12.775]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:35:12.775]        </block>
[22:35:12.775]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:35:12.776]      </control>
[22:35:12.776]      // end if-block "powered_down"
[22:35:12.776]    </control>
[22:35:12.776]    <block atomic="false" info="">
[22:35:12.776]      __var DEBUG_PORT_VAL    = 0;
[22:35:12.776]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:35:12.776]      __var ACCESS_POINT_VAL  = 0;
[22:35:12.776]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:35:12.776]      __ap = 1; 
[22:35:12.776]        // -> [__ap <= 0x00000001]
[22:35:12.776]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:35:12.780]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:35:12.780]      __ap = 4;
[22:35:12.780]        // -> [__ap <= 0x00000004]
[22:35:12.781]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:35:12.785]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[22:35:12.785]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[22:35:12.785]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:35:12.786]    </block>
[22:35:12.786]    <block atomic="false" info="">
[22:35:12.786]      __var nReset = 0x80;
[22:35:12.786]        // -> [nReset <= 0x00000080]
[22:35:12.787]      __var canReadPins = 0;
[22:35:12.787]        // -> [canReadPins <= 0x00000000]
[22:35:12.787]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:35:12.789]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:35:12.789]        // -> [canReadPins <= 0x00000001]
[22:35:12.789]    </block>
[22:35:12.789]    <control if="" while="1" timeout="200" info="">
[22:35:12.789]      // while "1"  (timeout="200")
[22:35:12.789]      // while-condition  =>  TRUE
[22:35:12.789]      // while "1"  (timeout="200")
[22:35:12.789]      // while-condition  =>  TRUE
[22:35:12.789]      // while "1"  (timeout="200")
[22:35:12.789]      // while-condition  =>  TRUE
[22:35:12.789]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.790]      // while-condition  =>  TRUE
[22:35:12.790]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.791]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.791]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.791]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.791]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.791]      // while "1"  (timeout="200")
[22:35:12.791]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.792]      // while-condition  =>  TRUE
[22:35:12.792]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.793]      // while-condition  =>  TRUE
[22:35:12.793]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.794]      // while "1"  (timeout="200")
[22:35:12.794]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.795]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.795]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.795]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.795]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.795]      // while-condition  =>  TRUE
[22:35:12.795]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.796]      // while-condition  =>  TRUE
[22:35:12.796]      // while "1"  (timeout="200")
[22:35:12.797]      // while-condition  =>  TRUE
[22:35:12.797]      // while "1"  (timeout="200")
[22:35:12.797]      // while  =>  TIMEOUT
[22:35:12.797]      // end while "1"
[22:35:12.797]    </control>
[22:35:12.797]    <control if="canReadPins" while="" timeout="0" info="">
[22:35:12.797]      // if-block "canReadPins"
[22:35:12.797]        // =>  TRUE
[22:35:12.797]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:35:12.797]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:35:12.799]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:35:12.799]        // while-condition  =>  TRUE
[22:35:12.799]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:35:12.802]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:35:12.802]        // while-condition  =>  FALSE
[22:35:12.802]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:35:12.802]      </control>
[22:35:12.802]      // end if-block "canReadPins"
[22:35:12.802]    </control>
[22:35:12.802]    <control if="!canReadPins" while="" timeout="0" info="">
[22:35:12.803]      // if-block "!canReadPins"
[22:35:12.803]        // =>  FALSE
[22:35:12.803]      // skip if-block "!canReadPins"
[22:35:12.803]    </control>
[22:35:12.803]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:35:12.803]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:35:12.803]        // =>  TRUE
[22:35:12.803]      <block atomic="false" info="">
[22:35:12.803]        WriteAP(0x00, 0x190008);
[22:35:12.805]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[22:35:12.805]        WriteAP(0xF0, 0x01);
[22:35:12.808]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:35:12.808]      </block>
[22:35:12.808]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:35:12.808]    </control>
[22:35:12.809]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:35:12.809]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:35:12.809]        // =>  FALSE
[22:35:12.809]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:35:12.809]    </control>
[22:35:12.809]    <block atomic="false" info="">
[22:35:12.809]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:35:12.813]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:35:12.813]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:35:12.813]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:35:12.817]      __ap = 0; //lets make sure we reset the access point selection
[22:35:12.817]        // -> [__ap <= 0x00000000]
[22:35:12.817]    </block>
[22:35:12.817]  </sequence>
[22:35:12.817]  
[22:35:12.837]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:35:12.837]  
[22:35:12.837]  <debugvars>
[22:35:12.838]    // Pre-defined
[22:35:12.838]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:35:12.838]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:35:12.838]    __dp=0x00000000
[22:35:12.838]    __ap=0x00000000
[22:35:12.838]    __traceout=0x00000000      (Trace Disabled)
[22:35:12.838]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:35:12.838]    __FlashAddr=0x00000000
[22:35:12.838]    __FlashLen=0x00000000
[22:35:12.838]    __FlashArg=0x00000000
[22:35:12.838]    __FlashOp=0x00000000
[22:35:12.838]    __Result=0x00000000
[22:35:12.839]  </debugvars>
[22:35:12.839]  
[22:35:12.839]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:35:12.839]    <block atomic="false" info="">
[22:35:12.839]      __var deviceID = 0;
[22:35:12.839]        // -> [deviceID <= 0x00000000]
[22:35:12.839]      __var version = 0;
[22:35:12.839]        // -> [version <= 0x00000000]
[22:35:12.839]      __var partNum = 0;
[22:35:12.839]        // -> [partNum <= 0x00000000]
[22:35:12.839]      __var manuf = 0;
[22:35:12.839]        // -> [manuf <= 0x00000000]
[22:35:12.840]      __var isMSPM0G1X0X_G3X0X = 0;
[22:35:12.840]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:35:12.840]      __var isProduction = 0;
[22:35:12.840]        // -> [isProduction <= 0x00000000]
[22:35:12.840]      __var continueId = 0;
[22:35:12.840]        // -> [continueId <= 0x00000000]
[22:35:12.840]      deviceID =   Read32(0x41C40004);
[22:35:12.847]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:35:12.847]        // -> [deviceID <= 0x2BB8802F]
[22:35:12.847]      version = deviceID >> 28;
[22:35:12.847]        // -> [version <= 0x00000002]
[22:35:12.847]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:35:12.847]        // -> [partNum <= 0x0000BB88]
[22:35:12.848]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:35:12.848]        // -> [manuf <= 0x00000017]
[22:35:12.848]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:35:12.848]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:35:12.848]      isProduction = (version > 0);
[22:35:12.848]        // -> [isProduction <= 0x00000001]
[22:35:12.848]    </block>
[22:35:12.848]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:35:12.848]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:35:12.848]        // =>  FALSE
[22:35:12.848]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:35:12.848]    </control>
[22:35:12.849]    <control if="continueId == 4" while="" timeout="0" info="">
[22:35:12.849]      // if-block "continueId == 4"
[22:35:12.849]        // =>  FALSE
[22:35:12.849]      // skip if-block "continueId == 4"
[22:35:12.849]    </control>
[22:35:12.849]    <control if="!isProduction" while="" timeout="0" info="">
[22:35:12.849]      // if-block "!isProduction"
[22:35:12.849]        // =>  FALSE
[22:35:12.849]      // skip if-block "!isProduction"
[22:35:12.849]    </control>
[22:35:12.849]  </sequence>
[22:35:12.849]  
[22:36:14.086]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:36:14.086]  
[22:36:14.086]  <debugvars>
[22:36:14.086]    // Pre-defined
[22:36:14.087]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:14.087]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:36:14.087]    __dp=0x00000000
[22:36:14.087]    __ap=0x00000000
[22:36:14.087]    __traceout=0x00000000      (Trace Disabled)
[22:36:14.087]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:14.087]    __FlashAddr=0x00000000
[22:36:14.087]    __FlashLen=0x00000000
[22:36:14.087]    __FlashArg=0x00000000
[22:36:14.087]    __FlashOp=0x00000000
[22:36:14.087]    __Result=0x00000000
[22:36:14.088]  </debugvars>
[22:36:14.088]  
[22:36:14.088]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:36:14.088]    <block atomic="false" info="">
[22:36:14.088]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:36:14.088]        // -> [isSWJ <= 0x00000001]
[22:36:14.088]      __var hasDormant = __protocol & 0x00020000;
[22:36:14.088]        // -> [hasDormant <= 0x00000000]
[22:36:14.089]      __var protType   = __protocol & 0x0000FFFF;
[22:36:14.089]        // -> [protType <= 0x00000002]
[22:36:14.089]    </block>
[22:36:14.089]    <control if="protType == 1" while="" timeout="0" info="">
[22:36:14.089]      // if-block "protType == 1"
[22:36:14.089]        // =>  FALSE
[22:36:14.089]      // skip if-block "protType == 1"
[22:36:14.089]    </control>
[22:36:14.089]    <control if="protType == 2" while="" timeout="0" info="">
[22:36:14.089]      // if-block "protType == 2"
[22:36:14.089]        // =>  TRUE
[22:36:14.090]      <control if="isSWJ" while="" timeout="0" info="">
[22:36:14.090]        // if-block "isSWJ"
[22:36:14.090]          // =>  TRUE
[22:36:14.090]        <control if="hasDormant" while="" timeout="0" info="">
[22:36:14.090]          // if-block "hasDormant"
[22:36:14.090]            // =>  FALSE
[22:36:14.090]          // skip if-block "hasDormant"
[22:36:14.090]        </control>
[22:36:14.090]        <control if="!hasDormant" while="" timeout="0" info="">
[22:36:14.091]          // if-block "!hasDormant"
[22:36:14.091]            // =>  TRUE
[22:36:14.091]          <block atomic="false" info="">
[22:36:14.091]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:36:14.093]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:14.093]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:36:14.096]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:36:14.096]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:36:14.098]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:14.098]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:36:14.100]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:36:14.100]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:36:14.102]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:14.102]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:36:14.104]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:36:14.104]          </block>
[22:36:14.104]          // end if-block "!hasDormant"
[22:36:14.104]        </control>
[22:36:14.104]        // end if-block "isSWJ"
[22:36:14.104]      </control>
[22:36:14.104]      <control if="!isSWJ" while="" timeout="0" info="">
[22:36:14.104]        // if-block "!isSWJ"
[22:36:14.104]          // =>  FALSE
[22:36:14.105]        // skip if-block "!isSWJ"
[22:36:14.105]      </control>
[22:36:14.105]      <block atomic="false" info="">
[22:36:14.105]        ReadDP(0x0);
[22:36:14.107]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:36:14.107]      </block>
[22:36:14.107]      // end if-block "protType == 2"
[22:36:14.107]    </control>
[22:36:14.107]  </sequence>
[22:36:14.107]  
[22:36:14.111]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:36:14.111]  
[22:36:14.111]  <debugvars>
[22:36:14.111]    // Pre-defined
[22:36:14.111]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:14.111]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:36:14.111]    __dp=0x00000000
[22:36:14.111]    __ap=0x00000000
[22:36:14.111]    __traceout=0x00000000      (Trace Disabled)
[22:36:14.111]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:14.111]    __FlashAddr=0x00000000
[22:36:14.111]    __FlashLen=0x00000000
[22:36:14.111]    __FlashArg=0x00000000
[22:36:14.111]    __FlashOp=0x00000000
[22:36:14.111]    __Result=0x00000000
[22:36:14.111]  </debugvars>
[22:36:14.112]  
[22:36:14.112]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:36:14.112]    <block atomic="false" info="">
[22:36:14.112]      __var SW_DP_ABORT       = 0x0;
[22:36:14.112]        // -> [SW_DP_ABORT <= 0x00000000]
[22:36:14.112]      __var DP_CTRL_STAT      = 0x4;
[22:36:14.112]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:36:14.112]      __var DP_SELECT         = 0x8;
[22:36:14.112]        // -> [DP_SELECT <= 0x00000008]
[22:36:14.112]      __var powered_down      = 0;
[22:36:14.112]        // -> [powered_down <= 0x00000000]
[22:36:14.112]      WriteDP(DP_SELECT, 0x00000000);
[22:36:14.114]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:36:14.116]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:36:14.117]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:36:14.118]        // -> [powered_down <= 0x00000001]
[22:36:14.118]    </block>
[22:36:14.118]    <control if="powered_down" while="" timeout="0" info="">
[22:36:14.118]      // if-block "powered_down"
[22:36:14.118]        // =>  TRUE
[22:36:14.118]      <block atomic="false" info="">
[22:36:14.118]        Message(0, "Debug/System power-up request sent");
[22:36:14.119]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:36:14.121]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:36:14.122]      </block>
[22:36:14.122]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:36:14.122]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:36:14.123]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:36:14.123]        // while-condition  =>  FALSE
[22:36:14.123]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:36:14.123]      </control>
[22:36:14.123]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:36:14.124]        // if-block "(__protocol & 0xFFFF) == 1"
[22:36:14.124]          // =>  FALSE
[22:36:14.124]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:36:14.124]      </control>
[22:36:14.124]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:36:14.124]        // if-block "(__protocol & 0xFFFF) == 2"
[22:36:14.124]          // =>  TRUE
[22:36:14.124]        <block atomic="false" info="">
[22:36:14.124]          Message(0, "executing SWD power up");
[22:36:14.125]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:36:14.126]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:36:14.126]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:36:14.129]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:36:14.129]        </block>
[22:36:14.129]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:36:14.130]      </control>
[22:36:14.130]      // end if-block "powered_down"
[22:36:14.130]    </control>
[22:36:14.130]    <block atomic="false" info="">
[22:36:14.130]      __var DEBUG_PORT_VAL    = 0;
[22:36:14.130]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:36:14.130]      __var ACCESS_POINT_VAL  = 0;
[22:36:14.130]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:36:14.130]      __ap = 1; 
[22:36:14.130]        // -> [__ap <= 0x00000001]
[22:36:14.130]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:36:14.134]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:36:14.134]      __ap = 4;
[22:36:14.134]        // -> [__ap <= 0x00000004]
[22:36:14.135]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:36:14.138]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[22:36:14.138]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[22:36:14.138]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:36:14.140]    </block>
[22:36:14.140]    <block atomic="false" info="">
[22:36:14.140]      __var nReset = 0x80;
[22:36:14.140]        // -> [nReset <= 0x00000080]
[22:36:14.140]      __var canReadPins = 0;
[22:36:14.140]        // -> [canReadPins <= 0x00000000]
[22:36:14.140]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:36:14.141]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:36:14.141]        // -> [canReadPins <= 0x00000001]
[22:36:14.142]    </block>
[22:36:14.142]    <control if="" while="1" timeout="200" info="">
[22:36:14.142]      // while "1"  (timeout="200")
[22:36:14.142]      // while-condition  =>  TRUE
[22:36:14.142]      // while "1"  (timeout="200")
[22:36:14.142]      // while-condition  =>  TRUE
[22:36:14.142]      // while "1"  (timeout="200")
[22:36:14.142]      // while-condition  =>  TRUE
[22:36:14.142]      // while "1"  (timeout="200")
[22:36:14.142]      // while-condition  =>  TRUE
[22:36:14.142]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.143]      // while "1"  (timeout="200")
[22:36:14.143]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.144]      // while "1"  (timeout="200")
[22:36:14.144]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.145]      // while-condition  =>  TRUE
[22:36:14.145]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.146]      // while "1"  (timeout="200")
[22:36:14.146]      // while-condition  =>  TRUE
[22:36:14.147]      // while "1"  (timeout="200")
[22:36:14.147]      // while-condition  =>  TRUE
[22:36:14.147]      // while "1"  (timeout="200")
[22:36:14.147]      // while-condition  =>  TRUE
[22:36:14.147]      // while "1"  (timeout="200")
[22:36:14.147]      // while-condition  =>  TRUE
[22:36:14.147]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.148]      // while "1"  (timeout="200")
[22:36:14.148]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.150]      // while-condition  =>  TRUE
[22:36:14.150]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.151]      // while "1"  (timeout="200")
[22:36:14.151]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.152]      // while "1"  (timeout="200")
[22:36:14.152]      // while-condition  =>  TRUE
[22:36:14.157]      // while "1"  (timeout="200")
[22:36:14.157]      // while  =>  TIMEOUT
[22:36:14.157]      // end while "1"
[22:36:14.157]    </control>
[22:36:14.158]    <control if="canReadPins" while="" timeout="0" info="">
[22:36:14.158]      // if-block "canReadPins"
[22:36:14.158]        // =>  TRUE
[22:36:14.158]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:36:14.158]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:36:14.159]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:36:14.159]        // while-condition  =>  TRUE
[22:36:14.159]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:36:14.161]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:36:14.161]        // while-condition  =>  FALSE
[22:36:14.161]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:36:14.161]      </control>
[22:36:14.161]      // end if-block "canReadPins"
[22:36:14.161]    </control>
[22:36:14.161]    <control if="!canReadPins" while="" timeout="0" info="">
[22:36:14.161]      // if-block "!canReadPins"
[22:36:14.161]        // =>  FALSE
[22:36:14.161]      // skip if-block "!canReadPins"
[22:36:14.161]    </control>
[22:36:14.162]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:36:14.162]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:36:14.162]        // =>  TRUE
[22:36:14.162]      <block atomic="false" info="">
[22:36:14.162]        WriteAP(0x00, 0x190008);
[22:36:14.164]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[22:36:14.164]        WriteAP(0xF0, 0x01);
[22:36:14.168]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:36:14.168]      </block>
[22:36:14.168]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:36:14.168]    </control>
[22:36:14.168]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:36:14.168]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:36:14.168]        // =>  FALSE
[22:36:14.168]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:36:14.168]    </control>
[22:36:14.168]    <block atomic="false" info="">
[22:36:14.168]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:36:14.172]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:36:14.173]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:36:14.173]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:36:14.175]      __ap = 0; //lets make sure we reset the access point selection
[22:36:14.177]        // -> [__ap <= 0x00000000]
[22:36:14.177]    </block>
[22:36:14.177]  </sequence>
[22:36:14.177]  
[22:36:14.196]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:36:14.196]  
[22:36:14.196]  <debugvars>
[22:36:14.196]    // Pre-defined
[22:36:14.196]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:14.196]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:36:14.196]    __dp=0x00000000
[22:36:14.196]    __ap=0x00000000
[22:36:14.197]    __traceout=0x00000000      (Trace Disabled)
[22:36:14.197]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:14.197]    __FlashAddr=0x00000000
[22:36:14.197]    __FlashLen=0x00000000
[22:36:14.197]    __FlashArg=0x00000000
[22:36:14.197]    __FlashOp=0x00000000
[22:36:14.197]    __Result=0x00000000
[22:36:14.197]  </debugvars>
[22:36:14.197]  
[22:36:14.197]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:36:14.197]    <block atomic="false" info="">
[22:36:14.197]      __var deviceID = 0;
[22:36:14.198]        // -> [deviceID <= 0x00000000]
[22:36:14.198]      __var version = 0;
[22:36:14.198]        // -> [version <= 0x00000000]
[22:36:14.198]      __var partNum = 0;
[22:36:14.198]        // -> [partNum <= 0x00000000]
[22:36:14.198]      __var manuf = 0;
[22:36:14.198]        // -> [manuf <= 0x00000000]
[22:36:14.198]      __var isMSPM0G1X0X_G3X0X = 0;
[22:36:14.198]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:36:14.198]      __var isProduction = 0;
[22:36:14.198]        // -> [isProduction <= 0x00000000]
[22:36:14.198]      __var continueId = 0;
[22:36:14.198]        // -> [continueId <= 0x00000000]
[22:36:14.198]      deviceID =   Read32(0x41C40004);
[22:36:14.204]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:36:14.205]        // -> [deviceID <= 0x2BB8802F]
[22:36:14.205]      version = deviceID >> 28;
[22:36:14.205]        // -> [version <= 0x00000002]
[22:36:14.205]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:36:14.205]        // -> [partNum <= 0x0000BB88]
[22:36:14.205]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:36:14.205]        // -> [manuf <= 0x00000017]
[22:36:14.205]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:36:14.205]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:36:14.205]      isProduction = (version > 0);
[22:36:14.205]        // -> [isProduction <= 0x00000001]
[22:36:14.205]    </block>
[22:36:14.206]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:36:14.206]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:36:14.206]        // =>  FALSE
[22:36:14.206]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:36:14.206]    </control>
[22:36:14.206]    <control if="continueId == 4" while="" timeout="0" info="">
[22:36:14.206]      // if-block "continueId == 4"
[22:36:14.206]        // =>  FALSE
[22:36:14.206]      // skip if-block "continueId == 4"
[22:36:14.206]    </control>
[22:36:14.206]    <control if="!isProduction" while="" timeout="0" info="">
[22:36:14.207]      // if-block "!isProduction"
[22:36:14.207]        // =>  FALSE
[22:36:14.207]      // skip if-block "!isProduction"
[22:36:14.207]    </control>
[22:36:14.207]  </sequence>
[22:36:14.207]  
[22:36:28.133]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:36:28.133]  
[22:36:28.134]  <debugvars>
[22:36:28.134]    // Pre-defined
[22:36:28.135]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:28.135]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:36:28.135]    __dp=0x00000000
[22:36:28.135]    __ap=0x00000000
[22:36:28.135]    __traceout=0x00000000      (Trace Disabled)
[22:36:28.135]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:28.135]    __FlashAddr=0x00000000
[22:36:28.135]    __FlashLen=0x00000000
[22:36:28.135]    __FlashArg=0x00000000
[22:36:28.136]    __FlashOp=0x00000000
[22:36:28.136]    __Result=0x00000000
[22:36:28.136]  </debugvars>
[22:36:28.136]  
[22:36:28.136]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:36:28.136]    <block atomic="false" info="">
[22:36:28.136]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:36:28.136]        // -> [isSWJ <= 0x00000001]
[22:36:28.136]      __var hasDormant = __protocol & 0x00020000;
[22:36:28.136]        // -> [hasDormant <= 0x00000000]
[22:36:28.136]      __var protType   = __protocol & 0x0000FFFF;
[22:36:28.136]        // -> [protType <= 0x00000002]
[22:36:28.136]    </block>
[22:36:28.137]    <control if="protType == 1" while="" timeout="0" info="">
[22:36:28.137]      // if-block "protType == 1"
[22:36:28.137]        // =>  FALSE
[22:36:28.137]      // skip if-block "protType == 1"
[22:36:28.137]    </control>
[22:36:28.137]    <control if="protType == 2" while="" timeout="0" info="">
[22:36:28.137]      // if-block "protType == 2"
[22:36:28.137]        // =>  TRUE
[22:36:28.137]      <control if="isSWJ" while="" timeout="0" info="">
[22:36:28.137]        // if-block "isSWJ"
[22:36:28.137]          // =>  TRUE
[22:36:28.137]        <control if="hasDormant" while="" timeout="0" info="">
[22:36:28.137]          // if-block "hasDormant"
[22:36:28.137]            // =>  FALSE
[22:36:28.138]          // skip if-block "hasDormant"
[22:36:28.138]        </control>
[22:36:28.138]        <control if="!hasDormant" while="" timeout="0" info="">
[22:36:28.138]          // if-block "!hasDormant"
[22:36:28.138]            // =>  TRUE
[22:36:28.138]          <block atomic="false" info="">
[22:36:28.138]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:36:28.140]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:28.141]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:36:28.144]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:36:28.144]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:36:28.146]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:28.146]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:36:28.148]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:36:28.148]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:36:28.150]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:28.150]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:36:28.151]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:36:28.151]          </block>
[22:36:28.151]          // end if-block "!hasDormant"
[22:36:28.151]        </control>
[22:36:28.153]        // end if-block "isSWJ"
[22:36:28.153]      </control>
[22:36:28.153]      <control if="!isSWJ" while="" timeout="0" info="">
[22:36:28.153]        // if-block "!isSWJ"
[22:36:28.153]          // =>  FALSE
[22:36:28.153]        // skip if-block "!isSWJ"
[22:36:28.153]      </control>
[22:36:28.153]      <block atomic="false" info="">
[22:36:28.153]        ReadDP(0x0);
[22:36:28.167]  
[22:36:28.167]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:36:28.167]  
[22:36:28.168]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:36:28.168]      </block>
[22:36:28.168]      // end if-block "protType == 2"
[22:36:28.168]    </control>
[22:36:28.168]  </sequence>
[22:36:28.168]  
[22:36:54.034]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:36:54.034]  
[22:36:54.035]  <debugvars>
[22:36:54.035]    // Pre-defined
[22:36:54.035]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:54.035]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:36:54.035]    __dp=0x00000000
[22:36:54.035]    __ap=0x00000000
[22:36:54.035]    __traceout=0x00000000      (Trace Disabled)
[22:36:54.035]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:54.035]    __FlashAddr=0x00000000
[22:36:54.035]    __FlashLen=0x00000000
[22:36:54.035]    __FlashArg=0x00000000
[22:36:54.035]    __FlashOp=0x00000000
[22:36:54.035]    __Result=0x00000000
[22:36:54.037]  </debugvars>
[22:36:54.037]  
[22:36:54.037]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:36:54.037]    <block atomic="false" info="">
[22:36:54.037]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:36:54.037]        // -> [isSWJ <= 0x00000001]
[22:36:54.037]      __var hasDormant = __protocol & 0x00020000;
[22:36:54.037]        // -> [hasDormant <= 0x00000000]
[22:36:54.037]      __var protType   = __protocol & 0x0000FFFF;
[22:36:54.037]        // -> [protType <= 0x00000002]
[22:36:54.037]    </block>
[22:36:54.037]    <control if="protType == 1" while="" timeout="0" info="">
[22:36:54.037]      // if-block "protType == 1"
[22:36:54.037]        // =>  FALSE
[22:36:54.037]      // skip if-block "protType == 1"
[22:36:54.038]    </control>
[22:36:54.038]    <control if="protType == 2" while="" timeout="0" info="">
[22:36:54.038]      // if-block "protType == 2"
[22:36:54.038]        // =>  TRUE
[22:36:54.038]      <control if="isSWJ" while="" timeout="0" info="">
[22:36:54.038]        // if-block "isSWJ"
[22:36:54.038]          // =>  TRUE
[22:36:54.038]        <control if="hasDormant" while="" timeout="0" info="">
[22:36:54.038]          // if-block "hasDormant"
[22:36:54.038]            // =>  FALSE
[22:36:54.038]          // skip if-block "hasDormant"
[22:36:54.038]        </control>
[22:36:54.038]        <control if="!hasDormant" while="" timeout="0" info="">
[22:36:54.038]          // if-block "!hasDormant"
[22:36:54.039]            // =>  TRUE
[22:36:54.039]          <block atomic="false" info="">
[22:36:54.039]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:36:54.040]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:54.040]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:36:54.042]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:36:54.042]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:36:54.044]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:54.044]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:36:54.046]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:36:54.046]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:36:54.048]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:54.048]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:36:54.050]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:36:54.050]          </block>
[22:36:54.050]          // end if-block "!hasDormant"
[22:36:54.050]        </control>
[22:36:54.050]        // end if-block "isSWJ"
[22:36:54.050]      </control>
[22:36:54.050]      <control if="!isSWJ" while="" timeout="0" info="">
[22:36:54.050]        // if-block "!isSWJ"
[22:36:54.050]          // =>  FALSE
[22:36:54.051]        // skip if-block "!isSWJ"
[22:36:54.051]      </control>
[22:36:54.051]      <block atomic="false" info="">
[22:36:54.051]        ReadDP(0x0);
[22:36:54.064]  
[22:36:54.064]  !!! E310 : Debug access failed - cannot read DP register 0x00
[22:36:54.064]  
[22:36:54.066]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:36:54.066]      </block>
[22:36:54.066]      // end if-block "protType == 2"
[22:36:54.066]    </control>
[22:36:54.066]  </sequence>
[22:36:54.066]  
[22:36:58.817]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:36:58.817]  
[22:36:58.817]  <debugvars>
[22:36:58.818]    // Pre-defined
[22:36:58.818]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:58.818]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[22:36:58.818]    __dp=0x00000000
[22:36:58.818]    __ap=0x00000000
[22:36:58.818]    __traceout=0x00000000      (Trace Disabled)
[22:36:58.818]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:58.818]    __FlashAddr=0x00000000
[22:36:58.819]    __FlashLen=0x00000000
[22:36:58.819]    __FlashArg=0x00000000
[22:36:58.819]    __FlashOp=0x00000000
[22:36:58.819]    __Result=0x00000000
[22:36:58.819]  </debugvars>
[22:36:58.819]  
[22:36:58.819]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:36:58.819]    <block atomic="false" info="">
[22:36:58.819]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:36:58.819]        // -> [isSWJ <= 0x00000001]
[22:36:58.819]      __var hasDormant = __protocol & 0x00020000;
[22:36:58.819]        // -> [hasDormant <= 0x00000000]
[22:36:58.819]      __var protType   = __protocol & 0x0000FFFF;
[22:36:58.819]        // -> [protType <= 0x00000002]
[22:36:58.819]    </block>
[22:36:58.819]    <control if="protType == 1" while="" timeout="0" info="">
[22:36:58.820]      // if-block "protType == 1"
[22:36:58.820]        // =>  FALSE
[22:36:58.820]      // skip if-block "protType == 1"
[22:36:58.820]    </control>
[22:36:58.820]    <control if="protType == 2" while="" timeout="0" info="">
[22:36:58.820]      // if-block "protType == 2"
[22:36:58.820]        // =>  TRUE
[22:36:58.820]      <control if="isSWJ" while="" timeout="0" info="">
[22:36:58.820]        // if-block "isSWJ"
[22:36:58.820]          // =>  TRUE
[22:36:58.820]        <control if="hasDormant" while="" timeout="0" info="">
[22:36:58.820]          // if-block "hasDormant"
[22:36:58.820]            // =>  FALSE
[22:36:58.820]          // skip if-block "hasDormant"
[22:36:58.820]        </control>
[22:36:58.821]        <control if="!hasDormant" while="" timeout="0" info="">
[22:36:58.821]          // if-block "!hasDormant"
[22:36:58.821]            // =>  TRUE
[22:36:58.821]          <block atomic="false" info="">
[22:36:58.821]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:36:58.822]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:58.822]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:36:58.825]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:36:58.825]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:36:58.827]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:58.827]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:36:58.829]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:36:58.829]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:36:58.831]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:36:58.831]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:36:58.833]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:36:58.833]          </block>
[22:36:58.833]          // end if-block "!hasDormant"
[22:36:58.833]        </control>
[22:36:58.833]        // end if-block "isSWJ"
[22:36:58.833]      </control>
[22:36:58.833]      <control if="!isSWJ" while="" timeout="0" info="">
[22:36:58.834]        // if-block "!isSWJ"
[22:36:58.834]          // =>  FALSE
[22:36:58.834]        // skip if-block "!isSWJ"
[22:36:58.834]      </control>
[22:36:58.834]      <block atomic="false" info="">
[22:36:58.834]        ReadDP(0x0);
[22:36:58.836]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:36:58.836]      </block>
[22:36:58.836]      // end if-block "protType == 2"
[22:36:58.836]    </control>
[22:36:58.836]  </sequence>
[22:36:58.836]  
[22:36:58.840]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:36:58.840]  
[22:36:58.840]  <debugvars>
[22:36:58.840]    // Pre-defined
[22:36:58.840]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:58.840]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[22:36:58.840]    __dp=0x00000000
[22:36:58.840]    __ap=0x00000000
[22:36:58.840]    __traceout=0x00000000      (Trace Disabled)
[22:36:58.840]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:58.840]    __FlashAddr=0x00000000
[22:36:58.840]    __FlashLen=0x00000000
[22:36:58.840]    __FlashArg=0x00000000
[22:36:58.841]    __FlashOp=0x00000000
[22:36:58.841]    __Result=0x00000000
[22:36:58.841]  </debugvars>
[22:36:58.841]  
[22:36:58.841]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:36:58.841]    <block atomic="false" info="">
[22:36:58.841]      __var SW_DP_ABORT       = 0x0;
[22:36:58.841]        // -> [SW_DP_ABORT <= 0x00000000]
[22:36:58.841]      __var DP_CTRL_STAT      = 0x4;
[22:36:58.841]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:36:58.841]      __var DP_SELECT         = 0x8;
[22:36:58.841]        // -> [DP_SELECT <= 0x00000008]
[22:36:58.841]      __var powered_down      = 0;
[22:36:58.841]        // -> [powered_down <= 0x00000000]
[22:36:58.841]      WriteDP(DP_SELECT, 0x00000000);
[22:36:58.844]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:36:58.844]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:36:58.846]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:36:58.846]        // -> [powered_down <= 0x00000001]
[22:36:58.846]    </block>
[22:36:58.846]    <control if="powered_down" while="" timeout="0" info="">
[22:36:58.846]      // if-block "powered_down"
[22:36:58.846]        // =>  TRUE
[22:36:58.846]      <block atomic="false" info="">
[22:36:58.846]        Message(0, "Debug/System power-up request sent");
[22:36:58.851]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:36:58.853]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:36:58.853]      </block>
[22:36:58.853]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:36:58.853]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:36:58.855]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:36:58.855]        // while-condition  =>  FALSE
[22:36:58.855]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:36:58.855]      </control>
[22:36:58.855]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:36:58.855]        // if-block "(__protocol & 0xFFFF) == 1"
[22:36:58.855]          // =>  FALSE
[22:36:58.855]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:36:58.855]      </control>
[22:36:58.855]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:36:58.855]        // if-block "(__protocol & 0xFFFF) == 2"
[22:36:58.855]          // =>  TRUE
[22:36:58.855]        <block atomic="false" info="">
[22:36:58.855]          Message(0, "executing SWD power up");
[22:36:58.856]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:36:58.859]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:36:58.859]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:36:58.860]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:36:58.861]        </block>
[22:36:58.861]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:36:58.861]      </control>
[22:36:58.861]      // end if-block "powered_down"
[22:36:58.861]    </control>
[22:36:58.861]    <block atomic="false" info="">
[22:36:58.861]      __var DEBUG_PORT_VAL    = 0;
[22:36:58.861]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:36:58.861]      __var ACCESS_POINT_VAL  = 0;
[22:36:58.861]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:36:58.861]      __ap = 1; 
[22:36:58.862]        // -> [__ap <= 0x00000001]
[22:36:58.862]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:36:58.866]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:36:58.866]      __ap = 4;
[22:36:58.866]        // -> [__ap <= 0x00000004]
[22:36:58.866]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:36:58.870]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[22:36:58.870]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[22:36:58.870]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:36:58.871]    </block>
[22:36:58.871]    <block atomic="false" info="">
[22:36:58.872]      __var nReset = 0x80;
[22:36:58.872]        // -> [nReset <= 0x00000080]
[22:36:58.872]      __var canReadPins = 0;
[22:36:58.872]        // -> [canReadPins <= 0x00000000]
[22:36:58.872]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:36:58.874]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:36:58.874]        // -> [canReadPins <= 0x00000001]
[22:36:58.874]    </block>
[22:36:58.874]    <control if="" while="1" timeout="200" info="">
[22:36:58.874]      // while "1"  (timeout="200")
[22:36:58.874]      // while-condition  =>  TRUE
[22:36:58.874]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.875]      // while "1"  (timeout="200")
[22:36:58.875]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.876]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.876]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.876]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.876]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.876]      // while-condition  =>  TRUE
[22:36:58.876]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.877]      // while-condition  =>  TRUE
[22:36:58.877]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.878]      // while-condition  =>  TRUE
[22:36:58.878]      // while "1"  (timeout="200")
[22:36:58.879]      // while-condition  =>  TRUE
[22:36:58.879]      // while "1"  (timeout="200")
[22:36:58.879]      // while-condition  =>  TRUE
[22:36:58.879]      // while "1"  (timeout="200")
[22:36:58.879]      // while-condition  =>  TRUE
[22:36:58.879]      // while "1"  (timeout="200")
[22:36:58.879]      // while-condition  =>  TRUE
[22:36:58.879]      // while "1"  (timeout="200")
[22:36:58.879]      // while-condition  =>  TRUE
[22:36:58.879]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.881]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.881]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.881]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.881]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.881]      // while "1"  (timeout="200")
[22:36:58.881]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.882]      // while "1"  (timeout="200")
[22:36:58.882]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.883]      // while "1"  (timeout="200")
[22:36:58.883]      // while-condition  =>  TRUE
[22:36:58.884]      // while "1"  (timeout="200")
[22:36:58.884]      // while-condition  =>  TRUE
[22:36:58.884]      // while "1"  (timeout="200")
[22:36:58.884]      // while-condition  =>  TRUE
[22:36:58.884]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.885]      // while-condition  =>  TRUE
[22:36:58.885]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.886]      // while "1"  (timeout="200")
[22:36:58.886]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.887]      // while-condition  =>  TRUE
[22:36:58.887]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.888]      // while-condition  =>  TRUE
[22:36:58.888]      // while "1"  (timeout="200")
[22:36:58.889]      // while-condition  =>  TRUE
[22:36:58.889]      // while "1"  (timeout="200")
[22:36:58.889]      // while-condition  =>  TRUE
[22:36:58.889]      // while "1"  (timeout="200")
[22:36:58.889]      // while-condition  =>  TRUE
[22:36:58.889]      // while "1"  (timeout="200")
[22:36:58.889]      // while-condition  =>  TRUE
[22:36:58.889]      // while "1"  (timeout="200")
[22:36:58.889]      // while-condition  =>  TRUE
[22:36:58.890]      // while "1"  (timeout="200")
[22:36:58.890]      // while  =>  TIMEOUT
[22:36:58.890]      // end while "1"
[22:36:58.890]    </control>
[22:36:58.890]    <control if="canReadPins" while="" timeout="0" info="">
[22:36:58.890]      // if-block "canReadPins"
[22:36:58.890]        // =>  TRUE
[22:36:58.890]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:36:58.890]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:36:58.892]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:36:58.892]        // while-condition  =>  TRUE
[22:36:58.892]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:36:58.894]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:36:58.894]        // while-condition  =>  FALSE
[22:36:58.894]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:36:58.894]      </control>
[22:36:58.894]      // end if-block "canReadPins"
[22:36:58.894]    </control>
[22:36:58.894]    <control if="!canReadPins" while="" timeout="0" info="">
[22:36:58.894]      // if-block "!canReadPins"
[22:36:58.894]        // =>  FALSE
[22:36:58.894]      // skip if-block "!canReadPins"
[22:36:58.894]    </control>
[22:36:58.895]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:36:58.895]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:36:58.895]        // =>  TRUE
[22:36:58.895]      <block atomic="false" info="">
[22:36:58.895]        WriteAP(0x00, 0x190008);
[22:36:58.897]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[22:36:58.897]        WriteAP(0xF0, 0x01);
[22:36:58.901]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:36:58.901]      </block>
[22:36:58.901]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:36:58.901]    </control>
[22:36:58.901]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:36:58.902]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:36:58.902]        // =>  FALSE
[22:36:58.902]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:36:58.902]    </control>
[22:36:58.902]    <block atomic="false" info="">
[22:36:58.902]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:36:58.906]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:36:58.906]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:36:58.906]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:36:58.907]      __ap = 0; //lets make sure we reset the access point selection
[22:36:58.907]        // -> [__ap <= 0x00000000]
[22:36:58.907]    </block>
[22:36:58.907]  </sequence>
[22:36:58.907]  
[22:36:58.927]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:36:58.927]  
[22:36:58.927]  <debugvars>
[22:36:58.927]    // Pre-defined
[22:36:58.927]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:36:58.927]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[22:36:58.927]    __dp=0x00000000
[22:36:58.927]    __ap=0x00000000
[22:36:58.927]    __traceout=0x00000000      (Trace Disabled)
[22:36:58.927]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:36:58.927]    __FlashAddr=0x00000000
[22:36:58.929]    __FlashLen=0x00000000
[22:36:58.929]    __FlashArg=0x00000000
[22:36:58.929]    __FlashOp=0x00000000
[22:36:58.929]    __Result=0x00000000
[22:36:58.929]  </debugvars>
[22:36:58.929]  
[22:36:58.929]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:36:58.929]    <block atomic="false" info="">
[22:36:58.929]      __var deviceID = 0;
[22:36:58.929]        // -> [deviceID <= 0x00000000]
[22:36:58.929]      __var version = 0;
[22:36:58.929]        // -> [version <= 0x00000000]
[22:36:58.929]      __var partNum = 0;
[22:36:58.929]        // -> [partNum <= 0x00000000]
[22:36:58.929]      __var manuf = 0;
[22:36:58.929]        // -> [manuf <= 0x00000000]
[22:36:58.929]      __var isMSPM0G1X0X_G3X0X = 0;
[22:36:58.929]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:36:58.929]      __var isProduction = 0;
[22:36:58.929]        // -> [isProduction <= 0x00000000]
[22:36:58.929]      __var continueId = 0;
[22:36:58.930]        // -> [continueId <= 0x00000000]
[22:36:58.930]      deviceID =   Read32(0x41C40004);
[22:36:58.936]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:36:58.936]        // -> [deviceID <= 0x2BB8802F]
[22:36:58.936]      version = deviceID >> 28;
[22:36:58.936]        // -> [version <= 0x00000002]
[22:36:58.937]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:36:58.937]        // -> [partNum <= 0x0000BB88]
[22:36:58.937]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:36:58.937]        // -> [manuf <= 0x00000017]
[22:36:58.937]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:36:58.937]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:36:58.937]      isProduction = (version > 0);
[22:36:58.937]        // -> [isProduction <= 0x00000001]
[22:36:58.937]    </block>
[22:36:58.937]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:36:58.937]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:36:58.937]        // =>  FALSE
[22:36:58.938]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:36:58.938]    </control>
[22:36:58.938]    <control if="continueId == 4" while="" timeout="0" info="">
[22:36:58.938]      // if-block "continueId == 4"
[22:36:58.938]        // =>  FALSE
[22:36:58.938]      // skip if-block "continueId == 4"
[22:36:58.938]    </control>
[22:36:58.938]    <control if="!isProduction" while="" timeout="0" info="">
[22:36:58.938]      // if-block "!isProduction"
[22:36:58.938]        // =>  FALSE
[22:36:58.938]      // skip if-block "!isProduction"
[22:36:58.938]    </control>
[22:36:58.938]  </sequence>
[22:36:58.938]  
[22:37:13.916]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[22:37:13.916]  
[22:37:13.917]  <debugvars>
[22:37:13.917]    // Pre-defined
[22:37:13.917]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:37:13.917]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:37:13.917]    __dp=0x00000000
[22:37:13.917]    __ap=0x00000000
[22:37:13.918]    __traceout=0x00000000      (Trace Disabled)
[22:37:13.918]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:37:13.918]    __FlashAddr=0x00000000
[22:37:13.918]    __FlashLen=0x00000000
[22:37:13.918]    __FlashArg=0x00000000
[22:37:13.918]    __FlashOp=0x00000000
[22:37:13.918]    __Result=0x00000000
[22:37:13.918]  </debugvars>
[22:37:13.918]  
[22:37:13.919]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[22:37:13.919]    <block atomic="false" info="">
[22:37:13.919]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[22:37:13.919]        // -> [isSWJ <= 0x00000001]
[22:37:13.919]      __var hasDormant = __protocol & 0x00020000;
[22:37:13.919]        // -> [hasDormant <= 0x00000000]
[22:37:13.919]      __var protType   = __protocol & 0x0000FFFF;
[22:37:13.919]        // -> [protType <= 0x00000002]
[22:37:13.919]    </block>
[22:37:13.919]    <control if="protType == 1" while="" timeout="0" info="">
[22:37:13.919]      // if-block "protType == 1"
[22:37:13.920]        // =>  FALSE
[22:37:13.920]      // skip if-block "protType == 1"
[22:37:13.920]    </control>
[22:37:13.920]    <control if="protType == 2" while="" timeout="0" info="">
[22:37:13.920]      // if-block "protType == 2"
[22:37:13.920]        // =>  TRUE
[22:37:13.920]      <control if="isSWJ" while="" timeout="0" info="">
[22:37:13.920]        // if-block "isSWJ"
[22:37:13.920]          // =>  TRUE
[22:37:13.920]        <control if="hasDormant" while="" timeout="0" info="">
[22:37:13.920]          // if-block "hasDormant"
[22:37:13.920]            // =>  FALSE
[22:37:13.921]          // skip if-block "hasDormant"
[22:37:13.921]        </control>
[22:37:13.921]        <control if="!hasDormant" while="" timeout="0" info="">
[22:37:13.921]          // if-block "!hasDormant"
[22:37:13.921]            // =>  TRUE
[22:37:13.923]          <block atomic="false" info="">
[22:37:13.923]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[22:37:13.925]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:37:13.925]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[22:37:13.927]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[22:37:13.927]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[22:37:13.929]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:37:13.929]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[22:37:13.930]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[22:37:13.931]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[22:37:13.932]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[22:37:13.933]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[22:37:13.934]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[22:37:13.935]          </block>
[22:37:13.935]          // end if-block "!hasDormant"
[22:37:13.935]        </control>
[22:37:13.935]        // end if-block "isSWJ"
[22:37:13.935]      </control>
[22:37:13.935]      <control if="!isSWJ" while="" timeout="0" info="">
[22:37:13.935]        // if-block "!isSWJ"
[22:37:13.935]          // =>  FALSE
[22:37:13.935]        // skip if-block "!isSWJ"
[22:37:13.935]      </control>
[22:37:13.935]      <block atomic="false" info="">
[22:37:13.936]        ReadDP(0x0);
[22:37:13.937]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[22:37:13.937]      </block>
[22:37:13.937]      // end if-block "protType == 2"
[22:37:13.938]    </control>
[22:37:13.938]  </sequence>
[22:37:13.938]  
[22:37:13.943]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[22:37:13.943]  
[22:37:13.943]  <debugvars>
[22:37:13.943]    // Pre-defined
[22:37:13.943]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:37:13.943]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:37:13.943]    __dp=0x00000000
[22:37:13.943]    __ap=0x00000000
[22:37:13.944]    __traceout=0x00000000      (Trace Disabled)
[22:37:13.944]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:37:13.944]    __FlashAddr=0x00000000
[22:37:13.944]    __FlashLen=0x00000000
[22:37:13.944]    __FlashArg=0x00000000
[22:37:13.944]    __FlashOp=0x00000000
[22:37:13.944]    __Result=0x00000000
[22:37:13.944]  </debugvars>
[22:37:13.944]  
[22:37:13.944]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[22:37:13.944]    <block atomic="false" info="">
[22:37:13.944]      __var SW_DP_ABORT       = 0x0;
[22:37:13.945]        // -> [SW_DP_ABORT <= 0x00000000]
[22:37:13.945]      __var DP_CTRL_STAT      = 0x4;
[22:37:13.945]        // -> [DP_CTRL_STAT <= 0x00000004]
[22:37:13.945]      __var DP_SELECT         = 0x8;
[22:37:13.945]        // -> [DP_SELECT <= 0x00000008]
[22:37:13.945]      __var powered_down      = 0;
[22:37:13.945]        // -> [powered_down <= 0x00000000]
[22:37:13.945]      WriteDP(DP_SELECT, 0x00000000);
[22:37:13.947]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[22:37:13.947]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[22:37:13.949]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[22:37:13.949]        // -> [powered_down <= 0x00000001]
[22:37:13.949]    </block>
[22:37:13.949]    <control if="powered_down" while="" timeout="0" info="">
[22:37:13.949]      // if-block "powered_down"
[22:37:13.949]        // =>  TRUE
[22:37:13.949]      <block atomic="false" info="">
[22:37:13.949]        Message(0, "Debug/System power-up request sent");
[22:37:13.951]        WriteDP(DP_CTRL_STAT, 0x50000000);
[22:37:13.953]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[22:37:13.953]      </block>
[22:37:13.953]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[22:37:13.953]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[22:37:13.954]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[22:37:13.954]        // while-condition  =>  FALSE
[22:37:13.955]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[22:37:13.955]      </control>
[22:37:13.955]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[22:37:13.955]        // if-block "(__protocol & 0xFFFF) == 1"
[22:37:13.955]          // =>  FALSE
[22:37:13.955]        // skip if-block "(__protocol & 0xFFFF) == 1"
[22:37:13.955]      </control>
[22:37:13.955]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[22:37:13.955]        // if-block "(__protocol & 0xFFFF) == 2"
[22:37:13.955]          // =>  TRUE
[22:37:13.955]        <block atomic="false" info="">
[22:37:13.955]          Message(0, "executing SWD power up");
[22:37:13.956]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[22:37:13.958]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[22:37:13.959]          WriteDP(SW_DP_ABORT, 0x0000001E);
[22:37:13.960]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[22:37:13.960]        </block>
[22:37:13.961]        // end if-block "(__protocol & 0xFFFF) == 2"
[22:37:13.961]      </control>
[22:37:13.961]      // end if-block "powered_down"
[22:37:13.961]    </control>
[22:37:13.961]    <block atomic="false" info="">
[22:37:13.961]      __var DEBUG_PORT_VAL    = 0;
[22:37:13.961]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[22:37:13.961]      __var ACCESS_POINT_VAL  = 0;
[22:37:13.961]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[22:37:13.961]      __ap = 1; 
[22:37:13.961]        // -> [__ap <= 0x00000001]
[22:37:13.961]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[22:37:13.965]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[22:37:13.965]      __ap = 4;
[22:37:13.965]        // -> [__ap <= 0x00000004]
[22:37:13.965]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:37:13.970]        // -> [ReadAP(0x00000000) => 0x00A20003]   (__dp=0x00000000, __ap=0x00000004)
[22:37:13.970]        // -> [ACCESS_POINT_VAL <= 0x00A20003]
[22:37:13.970]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:37:13.972]    </block>
[22:37:13.972]    <block atomic="false" info="">
[22:37:13.972]      __var nReset = 0x80;
[22:37:13.972]        // -> [nReset <= 0x00000080]
[22:37:13.972]      __var canReadPins = 0;
[22:37:13.972]        // -> [canReadPins <= 0x00000000]
[22:37:13.972]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[22:37:13.974]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:37:13.974]        // -> [canReadPins <= 0x00000001]
[22:37:13.974]    </block>
[22:37:13.974]    <control if="" while="1" timeout="200" info="">
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.975]      // while-condition  =>  TRUE
[22:37:13.975]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.976]      // while-condition  =>  TRUE
[22:37:13.976]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.977]      // while "1"  (timeout="200")
[22:37:13.977]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.978]      // while-condition  =>  TRUE
[22:37:13.978]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.979]      // while "1"  (timeout="200")
[22:37:13.979]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.980]      // while "1"  (timeout="200")
[22:37:13.980]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.981]      // while-condition  =>  TRUE
[22:37:13.981]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.982]      // while-condition  =>  TRUE
[22:37:13.982]      // while "1"  (timeout="200")
[22:37:13.983]      // while-condition  =>  TRUE
[22:37:13.983]      // while "1"  (timeout="200")
[22:37:13.983]      // while  =>  TIMEOUT
[22:37:13.983]      // end while "1"
[22:37:13.983]    </control>
[22:37:13.983]    <control if="canReadPins" while="" timeout="0" info="">
[22:37:13.983]      // if-block "canReadPins"
[22:37:13.983]        // =>  TRUE
[22:37:13.983]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[22:37:13.983]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:37:13.986]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[22:37:13.986]        // while-condition  =>  TRUE
[22:37:13.986]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[22:37:13.988]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[22:37:13.988]        // while-condition  =>  FALSE
[22:37:13.988]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[22:37:13.988]      </control>
[22:37:13.988]      // end if-block "canReadPins"
[22:37:13.988]    </control>
[22:37:13.988]    <control if="!canReadPins" while="" timeout="0" info="">
[22:37:13.988]      // if-block "!canReadPins"
[22:37:13.988]        // =>  FALSE
[22:37:13.988]      // skip if-block "!canReadPins"
[22:37:13.988]    </control>
[22:37:13.988]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[22:37:13.989]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:37:13.989]        // =>  FALSE
[22:37:13.989]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[22:37:13.989]    </control>
[22:37:13.989]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[22:37:13.989]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:37:13.989]        // =>  TRUE
[22:37:13.989]      <block atomic="false" info="">
[22:37:13.989]        WriteAP(0xF0, 0x01);
[22:37:13.993]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:37:13.993]        WriteAP(0x00, 0xF90008);
[22:37:13.997]          // -> [WriteAP(0x00000000, 0x00F90008)]   (__dp=0x00000000, __ap=0x00000004)
[22:37:13.997]        WriteAP(0xF0, 0x01);
[22:37:14.000]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[22:37:14.000]      </block>
[22:37:14.000]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[22:37:14.000]    </control>
[22:37:14.000]    <block atomic="false" info="">
[22:37:14.001]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[22:37:14.005]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[22:37:14.005]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[22:37:14.005]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[22:37:14.006]      __ap = 0; //lets make sure we reset the access point selection
[22:37:14.007]        // -> [__ap <= 0x00000000]
[22:37:14.007]    </block>
[22:37:14.007]  </sequence>
[22:37:14.007]  
[22:37:14.026]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[22:37:14.026]  
[22:37:14.027]  <debugvars>
[22:37:14.027]    // Pre-defined
[22:37:14.027]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[22:37:14.027]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[22:37:14.027]    __dp=0x00000000
[22:37:14.027]    __ap=0x00000000
[22:37:14.027]    __traceout=0x00000000      (Trace Disabled)
[22:37:14.027]    __errorcontrol=0x00000000  (Skip Errors="False")
[22:37:14.027]    __FlashAddr=0x00000000
[22:37:14.027]    __FlashLen=0x00000000
[22:37:14.028]    __FlashArg=0x00000000
[22:37:14.028]    __FlashOp=0x00000000
[22:37:14.028]    __Result=0x00000000
[22:37:14.028]  </debugvars>
[22:37:14.028]  
[22:37:14.028]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[22:37:14.028]    <block atomic="false" info="">
[22:37:14.028]      __var deviceID = 0;
[22:37:14.028]        // -> [deviceID <= 0x00000000]
[22:37:14.028]      __var version = 0;
[22:37:14.029]        // -> [version <= 0x00000000]
[22:37:14.029]      __var partNum = 0;
[22:37:14.029]        // -> [partNum <= 0x00000000]
[22:37:14.029]      __var manuf = 0;
[22:37:14.029]        // -> [manuf <= 0x00000000]
[22:37:14.029]      __var isMSPM0G1X0X_G3X0X = 0;
[22:37:14.029]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[22:37:14.029]      __var isProduction = 0;
[22:37:14.029]        // -> [isProduction <= 0x00000000]
[22:37:14.030]      __var continueId = 0;
[22:37:14.030]        // -> [continueId <= 0x00000000]
[22:37:14.030]      deviceID =   Read32(0x41C40004);
[22:37:14.037]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[22:37:14.037]        // -> [deviceID <= 0x2BB8802F]
[22:37:14.037]      version = deviceID >> 28;
[22:37:14.037]        // -> [version <= 0x00000002]
[22:37:14.037]      partNum = (deviceID & 0x0FFFF000) >> 12;
[22:37:14.037]        // -> [partNum <= 0x0000BB88]
[22:37:14.037]      manuf = (deviceID & 0x00000FFE) >> 1;
[22:37:14.038]        // -> [manuf <= 0x00000017]
[22:37:14.038]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[22:37:14.038]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[22:37:14.038]      isProduction = (version > 0);
[22:37:14.038]        // -> [isProduction <= 0x00000001]
[22:37:14.038]    </block>
[22:37:14.038]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[22:37:14.038]      // if-block "!isMSPM0G1X0X_G3X0X"
[22:37:14.038]        // =>  FALSE
[22:37:14.038]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[22:37:14.038]    </control>
[22:37:14.038]    <control if="continueId == 4" while="" timeout="0" info="">
[22:37:14.038]      // if-block "continueId == 4"
[22:37:14.038]        // =>  FALSE
[22:37:14.038]      // skip if-block "continueId == 4"
[22:37:14.039]    </control>
[22:37:14.039]    <control if="!isProduction" while="" timeout="0" info="">
[22:37:14.039]      // if-block "!isProduction"
[22:37:14.039]        // =>  FALSE
[22:37:14.039]      // skip if-block "!isProduction"
[22:37:14.039]    </control>
[22:37:14.039]  </sequence>
[22:37:14.039]  
