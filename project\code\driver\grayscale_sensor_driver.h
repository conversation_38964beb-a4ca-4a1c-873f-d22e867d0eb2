#ifndef __GRAYSCALE_SENSOR_DRIVER_H__
#define __GRAYSCALE_SENSOR_DRIVER_H__

#include "zf_common_headfile.h"
#include "zf_device_ganwei_grayscale.h"

//====================================================配置参数====================================================
// 硬件引脚配置
#define SENSOR_ADDR0_PIN        A0          // 传感器地址0
#define SENSOR_ADDR1_PIN        A1          // 传感器地址1  
#define SENSOR_ADDR2_PIN        A2          // 传感器地址2
#define SENSOR_ADC_PIN          ADC_0       // 传感器ADC

// 传感器参数
#define SENSOR_THRESHOLD        2000        // 黑白阈值
#define SENSOR_COUNT            8           // 传感器数量

// 传感器权重（用于计算线位置，针对1.8cm黑线优化）
static const float POSITION_WEIGHTS[8] = {-4.0f, -2.86f, -1.71f, -0.57f, 0.57f, 1.71f, 2.86f, 4.0f};

//====================================================数据结构====================================================
typedef struct {
    uint8 digital_data;         // 8位数字数据
    uint16 analog_data;         // 模拟数据
    float line_position;        // 线位置 (-4.0 ~ +4.0)
    uint8 line_detected;        // 是否检测到线
    uint32 last_detect_time;    // 上次检测到线的时间
} sensor_data_t;

//====================================================函数声明====================================================
/**
 * @brief 传感器初始化
 * @return 成功返回1，失败返回0
 */
uint8 sensor_init(void);

/**
 * @brief 读取传感器数据
 * @return 传感器数据结构体
 */
sensor_data_t sensor_read(void);

/**
 * @brief 获取线位置
 * @return 线位置 (-4.0表示最左，+4.0表示最右，0表示中心)
 */
float sensor_get_line_position(void);

/**
 * @brief 检查是否检测到线
 * @return 检测到返回1，未检测到返回0
 */
uint8 sensor_is_line_detected(void);

#endif // __GRAYSCALE_SENSOR_DRIVER_H__
