/*********************************************************************************************************************
* MSPM0G3507 Opensource Library ����MSPM0G3507 ��Դ�⣩��һ�����ڹٷ� SDK �ӿڵĵ�������Դ��
* Copyright (c) 2022 SEEKFREE ��ɿƼ�
* 
* ���ļ��� MSPM0G3507 ��Դ���һ����
* 
* MSPM0G3507 ��Դ�� ���������
* �����Ը���������������ᷢ���� GPL��GNU General Public License���� GNUͨ�ù�������֤��������
* �� GPL �ĵ�3�棨�� GPL3.0������ѡ��ģ��κκ����İ汾�����·�����/���޸���
* 
* ����Դ��ķ�����ϣ�����ܷ������ã�����δ�������κεı�֤
* ����û�������������Ի��ʺ��ض���;�ı�֤
* ����ϸ����μ� GPL
* 
* ��Ӧ�����յ�����Դ���ͬʱ�յ�һ�� GPL �ĸ���
* ���û�У������<https://www.gnu.org/licenses/>
* 
* ����ע����
* ����Դ��ʹ�� GPL3.0 ��Դ����֤Э�� ������������Ϊ���İ汾
* ��������Ӣ�İ��� libraries/doc �ļ����µ� GPL3_permission_statement.txt �ļ���
* ����֤������ libraries �ļ����� �����ļ����µ� LICENSE �ļ�
* ��ӭ��λʹ�ò����������� ���޸�����ʱ���뱣����ɿƼ��İ�Ȩ����������������
* 
* �ļ�����          zf_device_ganwei_grayscale.c
* ��˾����          �ɶ���ɿƼ����޹�˾
* �汾��Ϣ          �鿴 libraries/doc �ļ����� version �ļ� �汾˵��
* ��������          MDK 5.37
* ����ƽ̨          MSPM0G3507
* ��������          https://seekfree.taobao.com/
*
* �޸ļ�¼
* ����              ����                ��ע
* 2025-01-01       SeekFree            ��Ϊ8·�Ҷȴ���������
********************************************************************************************************************/

#include "zf_device_ganwei_grayscale.h"
#include "zf_driver_gpio.h"
#include "zf_driver_adc.h"
#include "zf_driver_delay.h"
#include "zf_common_debug.h"

//-------------------------------------------------------------------------------------------------------------------
// �ڲ��������ɼ�8·ģ��ֵ����ֵ�˲�
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_get_analog_values(ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i, j;
    uint32 analog_sum = 0;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        // ��ַ��ȡ���߼����ö�Ӧͨ��
        gpio_set_level(dev_info->addr_pin0, !(i & 0x01));  // ��ַ��0
        gpio_set_level(dev_info->addr_pin1, !(i & 0x02));  // ��ַ��1
        gpio_set_level(dev_info->addr_pin2, !(i & 0x04));  // ��ַ��2
        
        // �ɼ�8��ADC�������ֵ
        analog_sum = 0;
        for(j = 0; j < 8; j++)
        {
            analog_sum += adc_convert((adc_pin_enum)(dev_info->adc_channel));  // ǿ��ת����������
        }
        
        if(!dev_info->direction_reverse)
        {
            result[i] = (uint16)(analog_sum / 8);
        }
        else
        {
            result[7 - i] = (uint16)(analog_sum / 8);
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
// �ڲ�������ģ��ֵת�����źţ���ֵ����
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_convert_to_digital(ganwei_grayscale_info_struct *dev_info)
{
    uint8 i;
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        if(dev_info->analog_value[i] > dev_info->gray_white[i])
        {
            dev_info->digital_value |= (1 << i);   // ��ɫ���Ϊ1
        }
        else if(dev_info->analog_value[i] < dev_info->gray_black[i])
        {
            dev_info->digital_value &= ~(1 << i);  // ��ɫ���Ϊ0
        }
        // �м�Ҷȱ���ԭ״̬
    }
}

//-------------------------------------------------------------------------------------------------------------------
// �ڲ���������һ��ADCֵ��ָ����Χ
//-------------------------------------------------------------------------------------------------------------------
static void ganwei_grayscale_normalize_values(ganwei_grayscale_info_struct *dev_info)
{
    uint8 i;
    uint16 normalized_value;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        if(dev_info->analog_value[i] < dev_info->calibrated_black[i])
        {
            normalized_value = 0;
        }
        else
        {
            normalized_value = (uint16)((dev_info->analog_value[i] - dev_info->calibrated_black[i]) * dev_info->normal_factor[i]);
        }
        
        if(normalized_value > (uint16)(dev_info->adc_max_value))
        {
            normalized_value = (uint16)(dev_info->adc_max_value);
        }
        
        dev_info->normal_value[i] = normalized_value;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// ��������ʼ��������
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_init(ganwei_grayscale_info_struct *dev_info,
                            ganwei_grayscale_edition_enum edition,
                            ganwei_grayscale_adc_bits_enum adc_bits,
                            gpio_pin_enum addr0,
                            gpio_pin_enum addr1,
                            gpio_pin_enum addr2,
                            adc_pin_enum adc_ch)
{
    uint8 i;
    
    memset(dev_info->analog_value, 0, sizeof(dev_info->analog_value));
    memset(dev_info->normal_value, 0, sizeof(dev_info->normal_value));
    memset(dev_info->calibrated_white, 0, sizeof(dev_info->calibrated_white));
    memset(dev_info->calibrated_black, 0, sizeof(dev_info->calibrated_black));
    memset(dev_info->gray_white, 0, sizeof(dev_info->gray_white));
    memset(dev_info->gray_black, 0, sizeof(dev_info->gray_black));
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        dev_info->normal_factor[i] = 0.0f;
    }
    
    dev_info->edition = edition;
    dev_info->adc_resolution = adc_bits;
    dev_info->addr_pin0 = addr0;
    dev_info->addr_pin1 = addr1;
    dev_info->addr_pin2 = addr2;
    dev_info->adc_channel = adc_ch;
    dev_info->direction_reverse = 0;
    
    switch(adc_bits)
    {
        case GANWEI_GRAYSCALE_ADC_8BITS:  dev_info->adc_max_value = 255.0f;   break;
        case GANWEI_GRAYSCALE_ADC_10BITS: dev_info->adc_max_value = 1023.0f;  break;
        case GANWEI_GRAYSCALE_ADC_12BITS: dev_info->adc_max_value = 4095.0f;  break;
        case GANWEI_GRAYSCALE_ADC_14BITS: dev_info->adc_max_value = 16383.0f; break;
        default: dev_info->adc_max_value = 4095.0f; break;
    }
    
    if(edition == GANWEI_GRAYSCALE_CLASS_EDITION)
        dev_info->timeout_value = 1;
    else
        dev_info->timeout_value = 10;
    
    dev_info->digital_value = 0;
    dev_info->tick_counter = 0;
    dev_info->init_flag = 0;
    
    gpio_init(addr0, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(addr1, GPO, GPIO_LOW, GPO_PUSH_PULL);
    gpio_init(addr2, GPO, GPIO_LOW, GPO_PUSH_PULL);
    
    adc_init((adc_pin_enum)adc_ch, ADC_12BIT);  // ǿ��ת��������
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// ��������У׼�����ĳ�ʼ��
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_init_with_calibration(ganwei_grayscale_info_struct *dev_info,
                                             const uint16 *white_values,
                                             const uint16 *black_values)
{
    uint8 i;
    uint16 temp;
    float normal_diff;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        uint16 white_val, black_val;

        // ������֤��ֵ > ��ֵ (ʹ�þֲ�������)
        if(black_values[i] >= white_values[i])
        {
            white_val = black_values[i];
            black_val = white_values[i];
        }
        else
        {
            white_val = white_values[i];
            black_val = black_values[i];
        }
        
        dev_info->gray_white[i] = (white_val * 2 + black_val) / 3;
        dev_info->gray_black[i] = (white_val + black_val * 2) / 3;

        dev_info->calibrated_black[i] = black_val;
        dev_info->calibrated_white[i] = white_val;
        
        if((white_val == 0 && black_val == 0) || (white_val == black_val))
        {
            dev_info->normal_factor[i] = 0.0f;
            continue;
        }

        normal_diff = (float)white_val - (float)black_val;
        dev_info->normal_factor[i] = dev_info->adc_max_value / normal_diff;
    }
    
    dev_info->init_flag = 1;
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// �����������񣬲ɼ�����������
//-------------------------------------------------------------------------------------------------------------------
void ganwei_grayscale_task(ganwei_grayscale_info_struct *dev_info)
{
    ganwei_grayscale_get_analog_values(dev_info, dev_info->analog_value);
    
    if(dev_info->init_flag)
    {
        ganwei_grayscale_convert_to_digital(dev_info);
        ganwei_grayscale_normalize_values(dev_info);
    }
}

//-------------------------------------------------------------------------------------------------------------------
// ��������ȡ�����ź�״̬
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_digital(ganwei_grayscale_info_struct *dev_info)
{
    return dev_info->digital_value;
}

//-------------------------------------------------------------------------------------------------------------------
// ��������ȡģ��������
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_analog(ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i;
    ganwei_grayscale_get_analog_values(dev_info, dev_info->analog_value);
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        result[i] = dev_info->analog_value[i];
    }
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// ��������ȡ��һ������
//-------------------------------------------------------------------------------------------------------------------
uint8 ganwei_grayscale_get_normalized(ganwei_grayscale_info_struct *dev_info, uint16 *result)
{
    uint8 i;
    if(!dev_info->init_flag)
        return 0;
    
    for(i = 0; i < GANWEI_GRAYSCALE_CHANNEL_NUM; i++)
    {
        result[i] = dev_info->normal_value[i];
    }
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// �����������������
//-------------------------------------------------------------------------------------------------------------------
void ganwei_grayscale_set_direction(ganwei_grayscale_info_struct *dev_info, uint8 reverse)
{
    dev_info->direction_reverse = reverse;
}
